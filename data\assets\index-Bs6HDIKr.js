function e(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();var t,n,a={exports:{}},r={};var l,s,i=(n||(n=1,a.exports=function(){if(t)return r;t=1;var e=Symbol.for("react.transitional.element"),n=Symbol.for("react.fragment");function a(t,n,a){var r=null;if(void 0!==a&&(r=""+a),void 0!==n.key&&(r=""+n.key),"key"in n)for(var l in a={},n)"key"!==l&&(a[l]=n[l]);else a=n;return n=a.ref,{$$typeof:e,type:t,key:r,ref:void 0!==n?n:null,props:a}}return r.Fragment=n,r.jsx=a,r.jsxs=a,r}()),a.exports),o={exports:{}},u={};function c(){if(l)return u;l=1;var e=Symbol.for("react.transitional.element"),t=Symbol.for("react.portal"),n=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),r=Symbol.for("react.profiler"),s=Symbol.for("react.consumer"),i=Symbol.for("react.context"),o=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),m=Symbol.iterator;var p={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},h=Object.assign,g={};function b(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||p}function v(){}function x(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||p}b.prototype.isReactComponent={},b.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},b.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},v.prototype=b.prototype;var y=x.prototype=new v;y.constructor=x,h(y,b.prototype),y.isPureReactComponent=!0;var w=Array.isArray,S={H:null,A:null,T:null,S:null,V:null},k=Object.prototype.hasOwnProperty;function N(t,n,a,r,l,s){return a=s.ref,{$$typeof:e,type:t,key:n,ref:void 0!==a?a:null,props:s}}function j(t){return"object"==typeof t&&null!==t&&t.$$typeof===e}var C=/\/+/g;function E(e,t){return"object"==typeof e&&null!==e&&null!=e.key?(n=""+e.key,a={"=":"=0",":":"=2"},"$"+n.replace(/[=:]/g,(function(e){return a[e]}))):t.toString(36);var n,a}function T(){}function P(n,a,r,l,s){var i=typeof n;"undefined"!==i&&"boolean"!==i||(n=null);var o,u,c=!1;if(null===n)c=!0;else switch(i){case"bigint":case"string":case"number":c=!0;break;case"object":switch(n.$$typeof){case e:case t:c=!0;break;case f:return P((c=n._init)(n._payload),a,r,l,s)}}if(c)return s=s(n),c=""===l?"."+E(n,0):l,w(s)?(r="",null!=c&&(r=c.replace(C,"$&/")+"/"),P(s,a,r,"",(function(e){return e}))):null!=s&&(j(s)&&(o=s,u=r+(null==s.key||n&&n.key===s.key?"":(""+s.key).replace(C,"$&/")+"/")+c,s=N(o.type,u,void 0,0,0,o.props)),a.push(s)),1;c=0;var d,p=""===l?".":l+":";if(w(n))for(var h=0;h<n.length;h++)c+=P(l=n[h],a,r,i=p+E(l,h),s);else if("function"==typeof(h=null===(d=n)||"object"!=typeof d?null:"function"==typeof(d=m&&d[m]||d["@@iterator"])?d:null))for(n=h.call(n),h=0;!(l=n.next()).done;)c+=P(l=l.value,a,r,i=p+E(l,h++),s);else if("object"===i){if("function"==typeof n.then)return P(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"==typeof e.status?e.then(T,T):(e.status="pending",e.then((function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)}),(function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)}))),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(n),a,r,l,s);throw a=String(n),Error("Objects are not valid as a React child (found: "+("[object Object]"===a?"object with keys {"+Object.keys(n).join(", ")+"}":a)+"). If you meant to render a collection of children, use an array instead.")}return c}function z(e,t,n){if(null==e)return e;var a=[],r=0;return P(e,a,"","",(function(e){return t.call(n,e,r++)})),a}function L(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var _="function"==typeof reportError?reportError:function(e){if("object"==typeof window&&"function"==typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"==typeof e&&null!==e&&"string"==typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"==typeof process&&"function"==typeof process.emit)return void process.emit("uncaughtException",e)};function M(){}return u.Children={map:z,forEach:function(e,t,n){z(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return z(e,(function(){t++})),t},toArray:function(e){return z(e,(function(e){return e}))||[]},only:function(e){if(!j(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},u.Component=b,u.Fragment=n,u.Profiler=r,u.PureComponent=x,u.StrictMode=a,u.Suspense=c,u.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=S,u.__COMPILER_RUNTIME={__proto__:null,c:function(e){return S.H.useMemoCache(e)}},u.cache=function(e){return function(){return e.apply(null,arguments)}},u.cloneElement=function(e,t,n){if(null==e)throw Error("The argument must be a React element, but you passed "+e+".");var a=h({},e.props),r=e.key;if(null!=t)for(l in void 0!==t.ref&&void 0,void 0!==t.key&&(r=""+t.key),t)!k.call(t,l)||"key"===l||"__self"===l||"__source"===l||"ref"===l&&void 0===t.ref||(a[l]=t[l]);var l=arguments.length-2;if(1===l)a.children=n;else if(1<l){for(var s=Array(l),i=0;i<l;i++)s[i]=arguments[i+2];a.children=s}return N(e.type,r,void 0,0,0,a)},u.createContext=function(e){return(e={$$typeof:i,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider=e,e.Consumer={$$typeof:s,_context:e},e},u.createElement=function(e,t,n){var a,r={},l=null;if(null!=t)for(a in void 0!==t.key&&(l=""+t.key),t)k.call(t,a)&&"key"!==a&&"__self"!==a&&"__source"!==a&&(r[a]=t[a]);var s=arguments.length-2;if(1===s)r.children=n;else if(1<s){for(var i=Array(s),o=0;o<s;o++)i[o]=arguments[o+2];r.children=i}if(e&&e.defaultProps)for(a in s=e.defaultProps)void 0===r[a]&&(r[a]=s[a]);return N(e,l,void 0,0,0,r)},u.createRef=function(){return{current:null}},u.forwardRef=function(e){return{$$typeof:o,render:e}},u.isValidElement=j,u.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:L}},u.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},u.startTransition=function(e){var t=S.T,n={};S.T=n;try{var a=e(),r=S.S;null!==r&&r(n,a),"object"==typeof a&&null!==a&&"function"==typeof a.then&&a.then(M,_)}catch(l){_(l)}finally{S.T=t}},u.unstable_useCacheRefresh=function(){return S.H.useCacheRefresh()},u.use=function(e){return S.H.use(e)},u.useActionState=function(e,t,n){return S.H.useActionState(e,t,n)},u.useCallback=function(e,t){return S.H.useCallback(e,t)},u.useContext=function(e){return S.H.useContext(e)},u.useDebugValue=function(){},u.useDeferredValue=function(e,t){return S.H.useDeferredValue(e,t)},u.useEffect=function(e,t,n){var a=S.H;if("function"==typeof n)throw Error("useEffect CRUD overload is not enabled in this build of React.");return a.useEffect(e,t)},u.useId=function(){return S.H.useId()},u.useImperativeHandle=function(e,t,n){return S.H.useImperativeHandle(e,t,n)},u.useInsertionEffect=function(e,t){return S.H.useInsertionEffect(e,t)},u.useLayoutEffect=function(e,t){return S.H.useLayoutEffect(e,t)},u.useMemo=function(e,t){return S.H.useMemo(e,t)},u.useOptimistic=function(e,t){return S.H.useOptimistic(e,t)},u.useReducer=function(e,t,n){return S.H.useReducer(e,t,n)},u.useRef=function(e){return S.H.useRef(e)},u.useState=function(e){return S.H.useState(e)},u.useSyncExternalStore=function(e,t,n){return S.H.useSyncExternalStore(e,t,n)},u.useTransition=function(){return S.H.useTransition()},u.version="19.1.0",u}function d(){return s||(s=1,o.exports=c()),o.exports}var f=d();const m=e(f);var p,h,g={exports:{}},b={},v={exports:{}},x={};function y(){return h||(h=1,v.exports=(p||(p=1,function(e){function t(e,t){var n=e.length;e.push(t);e:for(;0<n;){var a=n-1>>>1,l=e[a];if(!(0<r(l,t)))break e;e[a]=t,e[n]=l,n=a}}function n(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var a=0,l=e.length,s=l>>>1;a<s;){var i=2*(a+1)-1,o=e[i],u=i+1,c=e[u];if(0>r(o,n))u<l&&0>r(c,o)?(e[a]=c,e[u]=n,a=u):(e[a]=o,e[i]=n,a=i);else{if(!(u<l&&0>r(c,n)))break e;e[a]=c,e[u]=n,a=u}}}return t}function r(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(e.unstable_now=void 0,"object"==typeof performance&&"function"==typeof performance.now){var l=performance;e.unstable_now=function(){return l.now()}}else{var s=Date,i=s.now();e.unstable_now=function(){return s.now()-i}}var o=[],u=[],c=1,d=null,f=3,m=!1,p=!1,h=!1,g=!1,b="function"==typeof setTimeout?setTimeout:null,v="function"==typeof clearTimeout?clearTimeout:null,x="undefined"!=typeof setImmediate?setImmediate:null;function y(e){for(var r=n(u);null!==r;){if(null===r.callback)a(u);else{if(!(r.startTime<=e))break;a(u),r.sortIndex=r.expirationTime,t(o,r)}r=n(u)}}function w(e){if(h=!1,y(e),!p)if(null!==n(o))p=!0,k||(k=!0,S());else{var t=n(u);null!==t&&L(w,t.startTime-e)}}var S,k=!1,N=-1,j=5,C=-1;function E(){return!(!g&&e.unstable_now()-C<j)}function T(){if(g=!1,k){var t=e.unstable_now();C=t;var r=!0;try{e:{p=!1,h&&(h=!1,v(N),N=-1),m=!0;var l=f;try{t:{for(y(t),d=n(o);null!==d&&!(d.expirationTime>t&&E());){var s=d.callback;if("function"==typeof s){d.callback=null,f=d.priorityLevel;var i=s(d.expirationTime<=t);if(t=e.unstable_now(),"function"==typeof i){d.callback=i,y(t),r=!0;break t}d===n(o)&&a(o),y(t)}else a(o);d=n(o)}if(null!==d)r=!0;else{var c=n(u);null!==c&&L(w,c.startTime-t),r=!1}}break e}finally{d=null,f=l,m=!1}r=void 0}}finally{r?S():k=!1}}}if("function"==typeof x)S=function(){x(T)};else if("undefined"!=typeof MessageChannel){var P=new MessageChannel,z=P.port2;P.port1.onmessage=T,S=function(){z.postMessage(null)}}else S=function(){b(T,0)};function L(t,n){N=b((function(){t(e.unstable_now())}),n)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(e){e.callback=null},e.unstable_forceFrameRate=function(e){0>e||125<e||(j=0<e?Math.floor(1e3/e):5)},e.unstable_getCurrentPriorityLevel=function(){return f},e.unstable_next=function(e){switch(f){case 1:case 2:case 3:var t=3;break;default:t=f}var n=f;f=t;try{return e()}finally{f=n}},e.unstable_requestPaint=function(){g=!0},e.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=f;f=e;try{return t()}finally{f=n}},e.unstable_scheduleCallback=function(a,r,l){var s=e.unstable_now();switch(l="object"==typeof l&&null!==l&&"number"==typeof(l=l.delay)&&0<l?s+l:s,a){case 1:var i=-1;break;case 2:i=250;break;case 5:i=1073741823;break;case 4:i=1e4;break;default:i=5e3}return a={id:c++,callback:r,priorityLevel:a,startTime:l,expirationTime:i=l+i,sortIndex:-1},l>s?(a.sortIndex=l,t(u,a),null===n(o)&&a===n(u)&&(h?(v(N),N=-1):h=!0,L(w,l-s))):(a.sortIndex=i,t(o,a),p||m||(p=!0,k||(k=!0,S()))),a},e.unstable_shouldYield=E,e.unstable_wrapCallback=function(e){var t=f;return function(){var n=f;f=t;try{return e.apply(this,arguments)}finally{f=n}}}}(x)),x)),v.exports}var w,S,k,N,j={exports:{}},C={};function E(){if(w)return C;w=1;var e=d();function t(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function n(){}var a={d:{f:n,r:function(){throw Error(t(522))},D:n,C:n,L:n,m:n,X:n,S:n,M:n},p:0,findDOMNode:null},r=Symbol.for("react.portal");var l=e.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function s(e,t){return"font"===e?"":"string"==typeof t?"use-credentials"===t?t:"":void 0}return C.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=a,C.createPortal=function(e,n){var a=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!n||1!==n.nodeType&&9!==n.nodeType&&11!==n.nodeType)throw Error(t(299));return function(e,t,n){var a=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:r,key:null==a?null:""+a,children:e,containerInfo:t,implementation:n}}(e,n,null,a)},C.flushSync=function(e){var t=l.T,n=a.p;try{if(l.T=null,a.p=2,e)return e()}finally{l.T=t,a.p=n,a.d.f()}},C.preconnect=function(e,t){"string"==typeof e&&(t?t="string"==typeof(t=t.crossOrigin)?"use-credentials"===t?t:"":void 0:t=null,a.d.C(e,t))},C.prefetchDNS=function(e){"string"==typeof e&&a.d.D(e)},C.preinit=function(e,t){if("string"==typeof e&&t&&"string"==typeof t.as){var n=t.as,r=s(n,t.crossOrigin),l="string"==typeof t.integrity?t.integrity:void 0,i="string"==typeof t.fetchPriority?t.fetchPriority:void 0;"style"===n?a.d.S(e,"string"==typeof t.precedence?t.precedence:void 0,{crossOrigin:r,integrity:l,fetchPriority:i}):"script"===n&&a.d.X(e,{crossOrigin:r,integrity:l,fetchPriority:i,nonce:"string"==typeof t.nonce?t.nonce:void 0})}},C.preinitModule=function(e,t){if("string"==typeof e)if("object"==typeof t&&null!==t){if(null==t.as||"script"===t.as){var n=s(t.as,t.crossOrigin);a.d.M(e,{crossOrigin:n,integrity:"string"==typeof t.integrity?t.integrity:void 0,nonce:"string"==typeof t.nonce?t.nonce:void 0})}}else null==t&&a.d.M(e)},C.preload=function(e,t){if("string"==typeof e&&"object"==typeof t&&null!==t&&"string"==typeof t.as){var n=t.as,r=s(n,t.crossOrigin);a.d.L(e,n,{crossOrigin:r,integrity:"string"==typeof t.integrity?t.integrity:void 0,nonce:"string"==typeof t.nonce?t.nonce:void 0,type:"string"==typeof t.type?t.type:void 0,fetchPriority:"string"==typeof t.fetchPriority?t.fetchPriority:void 0,referrerPolicy:"string"==typeof t.referrerPolicy?t.referrerPolicy:void 0,imageSrcSet:"string"==typeof t.imageSrcSet?t.imageSrcSet:void 0,imageSizes:"string"==typeof t.imageSizes?t.imageSizes:void 0,media:"string"==typeof t.media?t.media:void 0})}},C.preloadModule=function(e,t){if("string"==typeof e)if(t){var n=s(t.as,t.crossOrigin);a.d.m(e,{as:"string"==typeof t.as&&"script"!==t.as?t.as:void 0,crossOrigin:n,integrity:"string"==typeof t.integrity?t.integrity:void 0})}else a.d.m(e)},C.requestFormReset=function(e){a.d.r(e)},C.unstable_batchedUpdates=function(e,t){return e(t)},C.useFormState=function(e,t,n){return l.H.useFormState(e,t,n)},C.useFormStatus=function(){return l.H.useHostTransitionStatus()},C.version="19.1.0",C}function T(){if(k)return b;k=1;var e=y(),t=d(),n=(S||(S=1,function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){}}(),j.exports=E()),j.exports);function a(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function r(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function l(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{!!(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function s(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function i(e){if(l(e)!==e)throw Error(a(188))}function o(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e;for(e=e.child;null!==e;){if(null!==(t=o(e)))return t;e=e.sibling}return null}var u=Object.assign,c=Symbol.for("react.element"),f=Symbol.for("react.transitional.element"),m=Symbol.for("react.portal"),p=Symbol.for("react.fragment"),h=Symbol.for("react.strict_mode"),g=Symbol.for("react.profiler"),v=Symbol.for("react.provider"),x=Symbol.for("react.consumer"),w=Symbol.for("react.context"),N=Symbol.for("react.forward_ref"),C=Symbol.for("react.suspense"),T=Symbol.for("react.suspense_list"),P=Symbol.for("react.memo"),z=Symbol.for("react.lazy"),L=Symbol.for("react.activity"),_=Symbol.for("react.memo_cache_sentinel"),M=Symbol.iterator;function O(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=M&&e[M]||e["@@iterator"])?e:null}var F=Symbol.for("react.client.reference");function R(e){if(null==e)return null;if("function"==typeof e)return e.$$typeof===F?null:e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case p:return"Fragment";case g:return"Profiler";case h:return"StrictMode";case C:return"Suspense";case T:return"SuspenseList";case L:return"Activity"}if("object"==typeof e)switch(e.$$typeof){case m:return"Portal";case w:return(e.displayName||"Context")+".Provider";case x:return(e._context.displayName||"Context")+".Consumer";case N:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case P:return null!==(t=e.displayName||null)?t:R(e.type)||"Memo";case z:t=e._payload,e=e._init;try{return R(e(t))}catch(n){}}return null}var D=Array.isArray,A=t.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,I=n.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,$={pending:!1,data:null,method:null,action:null},U=[],V=-1;function B(e){return{current:e}}function H(e){0>V||(e.current=U[V],U[V]=null,V--)}function q(e,t){V++,U[V]=e.current,e.current=t}var W=B(null),Q=B(null),Z=B(null),G=B(null);function Y(e,t){switch(q(Z,t),q(Q,e),q(W,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?od(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)e=ud(t=od(t),e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}H(W),q(W,e)}function K(){H(W),H(Q),H(Z)}function X(e){null!==e.memoizedState&&q(G,e);var t=W.current,n=ud(t,e.type);t!==n&&(q(Q,e),q(W,n))}function J(e){Q.current===e&&(H(W),H(Q)),G.current===e&&(H(G),Xd._currentValue=$)}var ee=Object.prototype.hasOwnProperty,te=e.unstable_scheduleCallback,ne=e.unstable_cancelCallback,ae=e.unstable_shouldYield,re=e.unstable_requestPaint,le=e.unstable_now,se=e.unstable_getCurrentPriorityLevel,ie=e.unstable_ImmediatePriority,oe=e.unstable_UserBlockingPriority,ue=e.unstable_NormalPriority,ce=e.unstable_LowPriority,de=e.unstable_IdlePriority,fe=e.log,me=e.unstable_setDisableYieldValue,pe=null,he=null;function ge(e){if("function"==typeof fe&&me(e),he&&"function"==typeof he.setStrictMode)try{he.setStrictMode(pe,e)}catch(t){}}var be=Math.clz32?Math.clz32:function(e){return 0===(e>>>=0)?32:31-(ve(e)/xe|0)|0},ve=Math.log,xe=Math.LN2;var ye=256,we=4194304;function Se(e){var t=42&e;if(0!==t)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194048&e;case 4194304:case 8388608:case 16777216:case 33554432:return 62914560&e;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function ke(e,t,n){var a=e.pendingLanes;if(0===a)return 0;var r=0,l=e.suspendedLanes,s=e.pingedLanes;e=e.warmLanes;var i=134217727&a;return 0!==i?0!==(a=i&~l)?r=Se(a):0!==(s&=i)?r=Se(s):n||0!==(n=i&~e)&&(r=Se(n)):0!==(i=a&~l)?r=Se(i):0!==s?r=Se(s):n||0!==(n=a&~e)&&(r=Se(n)),0===r?0:0!==t&&t!==r&&0===(t&l)&&((l=r&-r)>=(n=t&-t)||32===l&&4194048&n)?t:r}function Ne(e,t){return 0===(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)}function je(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function Ce(){var e=ye;return!(4194048&(ye<<=1))&&(ye=256),e}function Ee(){var e=we;return!(62914560&(we<<=1))&&(we=4194304),e}function Te(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Pe(e,t){e.pendingLanes|=t,268435456!==t&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function ze(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var a=31-be(t);e.entangledLanes|=t,e.entanglements[a]=1073741824|e.entanglements[a]|4194090&n}function Le(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var a=31-be(n),r=1<<a;r&t|e[a]&t&&(e[a]|=t),n&=~r}}function _e(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Me(e){return 2<(e&=-e)?8<e?134217727&e?32:268435456:8:2}function Oe(){var e=I.p;return 0!==e?e:void 0===(e=window.event)?32:pf(e.type)}var Fe=Math.random().toString(36).slice(2),Re="__reactFiber$"+Fe,De="__reactProps$"+Fe,Ae="__reactContainer$"+Fe,Ie="__reactEvents$"+Fe,$e="__reactListeners$"+Fe,Ue="__reactHandles$"+Fe,Ve="__reactResources$"+Fe,Be="__reactMarker$"+Fe;function He(e){delete e[Re],delete e[De],delete e[Ie],delete e[$e],delete e[Ue]}function qe(e){var t=e[Re];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Ae]||n[Re]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=kd(e);null!==e;){if(n=e[Re])return n;e=kd(e)}return t}n=(e=n).parentNode}return null}function We(e){if(e=e[Re]||e[Ae]){var t=e.tag;if(5===t||6===t||13===t||26===t||27===t||3===t)return e}return null}function Qe(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e.stateNode;throw Error(a(33))}function Ze(e){var t=e[Ve];return t||(t=e[Ve]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Ge(e){e[Be]=!0}var Ye=new Set,Ke={};function Xe(e,t){Je(e,t),Je(e+"Capture",t)}function Je(e,t){for(Ke[e]=t,e=0;e<t.length;e++)Ye.add(t[e])}var et,tt,nt=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),at={},rt={};function lt(e,t,n){if(r=t,ee.call(rt,r)||!ee.call(at,r)&&(nt.test(r)?rt[r]=!0:(at[r]=!0,0)))if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":return void e.removeAttribute(t);case"boolean":var a=t.toLowerCase().slice(0,5);if("data-"!==a&&"aria-"!==a)return void e.removeAttribute(t)}e.setAttribute(t,""+n)}var r}function st(e,t,n){if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(t)}e.setAttribute(t,""+n)}}function it(e,t,n,a){if(null===a)e.removeAttribute(n);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(n)}e.setAttributeNS(t,n,""+a)}}function ot(e){if(void 0===et)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);et=t&&t[1]||"",tt=-1<n.stack.indexOf("\n    at")?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return"\n"+et+e+tt}var ut=!1;function ct(e,t){if(!e||ut)return"";ut=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(t){var n=function(){throw Error()};if(Object.defineProperty(n.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(n,[])}catch(r){var a=r}Reflect.construct(e,[],n)}else{try{n.call()}catch(l){a=l}e.call(n.prototype)}}else{try{throw Error()}catch(s){a=s}(n=e())&&"function"==typeof n.catch&&n.catch((function(){}))}}catch(i){if(i&&a&&"string"==typeof i.stack)return[i.stack,a.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var r=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");r&&r.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var l=a.DetermineComponentFrameRoot(),s=l[0],i=l[1];if(s&&i){var o=s.split("\n"),u=i.split("\n");for(r=a=0;a<o.length&&!o[a].includes("DetermineComponentFrameRoot");)a++;for(;r<u.length&&!u[r].includes("DetermineComponentFrameRoot");)r++;if(a===o.length||r===u.length)for(a=o.length-1,r=u.length-1;1<=a&&0<=r&&o[a]!==u[r];)r--;for(;1<=a&&0<=r;a--,r--)if(o[a]!==u[r]){if(1!==a||1!==r)do{if(a--,0>--r||o[a]!==u[r]){var c="\n"+o[a].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}}while(1<=a&&0<=r);break}}}finally{ut=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?ot(n):""}function dt(e){switch(e.tag){case 26:case 27:case 5:return ot(e.type);case 16:return ot("Lazy");case 13:return ot("Suspense");case 19:return ot("SuspenseList");case 0:case 15:return ct(e.type,!1);case 11:return ct(e.type.render,!1);case 1:return ct(e.type,!0);case 31:return ot("Activity");default:return""}}function ft(e){try{var t="";do{t+=dt(e),e=e.return}while(e);return t}catch(n){return"\nError generating stack: "+n.message+"\n"+n.stack}}function mt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function pt(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function ht(e){e._valueTracker||(e._valueTracker=function(e){var t=pt(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),a=""+e[t];if(!e.hasOwnProperty(t)&&void 0!==n&&"function"==typeof n.get&&"function"==typeof n.set){var r=n.get,l=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return r.call(this)},set:function(e){a=""+e,l.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return a},setValue:function(e){a=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function gt(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),a="";return e&&(a=pt(e)?e.checked?"true":"false":e.value),(e=a)!==n&&(t.setValue(e),!0)}function bt(e){if(void 0===(e=e||("undefined"!=typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}var vt=/[\n"\\]/g;function xt(e){return e.replace(vt,(function(e){return"\\"+e.charCodeAt(0).toString(16)+" "}))}function yt(e,t,n,a,r,l,s,i){e.name="",null!=s&&"function"!=typeof s&&"symbol"!=typeof s&&"boolean"!=typeof s?e.type=s:e.removeAttribute("type"),null!=t?"number"===s?(0===t&&""===e.value||e.value!=t)&&(e.value=""+mt(t)):e.value!==""+mt(t)&&(e.value=""+mt(t)):"submit"!==s&&"reset"!==s||e.removeAttribute("value"),null!=t?St(e,s,mt(t)):null!=n?St(e,s,mt(n)):null!=a&&e.removeAttribute("value"),null==r&&null!=l&&(e.defaultChecked=!!l),null!=r&&(e.checked=r&&"function"!=typeof r&&"symbol"!=typeof r),null!=i&&"function"!=typeof i&&"symbol"!=typeof i&&"boolean"!=typeof i?e.name=""+mt(i):e.removeAttribute("name")}function wt(e,t,n,a,r,l,s,i){if(null!=l&&"function"!=typeof l&&"symbol"!=typeof l&&"boolean"!=typeof l&&(e.type=l),null!=t||null!=n){if(("submit"===l||"reset"===l)&&null==t)return;n=null!=n?""+mt(n):"",t=null!=t?""+mt(t):n,i||t===e.value||(e.value=t),e.defaultValue=t}a="function"!=typeof(a=null!=a?a:r)&&"symbol"!=typeof a&&!!a,e.checked=i?e.checked:!!a,e.defaultChecked=!!a,null!=s&&"function"!=typeof s&&"symbol"!=typeof s&&"boolean"!=typeof s&&(e.name=s)}function St(e,t,n){"number"===t&&bt(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function kt(e,t,n,a){if(e=e.options,t){t={};for(var r=0;r<n.length;r++)t["$"+n[r]]=!0;for(n=0;n<e.length;n++)r=t.hasOwnProperty("$"+e[n].value),e[n].selected!==r&&(e[n].selected=r),r&&a&&(e[n].defaultSelected=!0)}else{for(n=""+mt(n),t=null,r=0;r<e.length;r++){if(e[r].value===n)return e[r].selected=!0,void(a&&(e[r].defaultSelected=!0));null!==t||e[r].disabled||(t=e[r])}null!==t&&(t.selected=!0)}}function Nt(e,t,n){null==t||((t=""+mt(t))!==e.value&&(e.value=t),null!=n)?e.defaultValue=null!=n?""+mt(n):"":e.defaultValue!==t&&(e.defaultValue=t)}function jt(e,t,n,r){if(null==t){if(null!=r){if(null!=n)throw Error(a(92));if(D(r)){if(1<r.length)throw Error(a(93));r=r[0]}n=r}null==n&&(n=""),t=n}n=mt(t),e.defaultValue=n,(r=e.textContent)===n&&""!==r&&null!==r&&(e.value=r)}function Ct(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var Et=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Tt(e,t,n){var a=0===t.indexOf("--");null==n||"boolean"==typeof n||""===n?a?e.setProperty(t,""):"float"===t?e.cssFloat="":e[t]="":a?e.setProperty(t,n):"number"!=typeof n||0===n||Et.has(t)?"float"===t?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function Pt(e,t,n){if(null!=t&&"object"!=typeof t)throw Error(a(62));if(e=e.style,null!=n){for(var r in n)!n.hasOwnProperty(r)||null!=t&&t.hasOwnProperty(r)||(0===r.indexOf("--")?e.setProperty(r,""):"float"===r?e.cssFloat="":e[r]="");for(var l in t)r=t[l],t.hasOwnProperty(l)&&n[l]!==r&&Tt(e,l,r)}else for(var s in t)t.hasOwnProperty(s)&&Tt(e,s,t[s])}function zt(e){if(-1===e.indexOf("-"))return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Lt=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),_t=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Mt(e){return _t.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Ot=null;function Ft(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var Rt=null,Dt=null;function At(e){var t=We(e);if(t&&(e=t.stateNode)){var n=e[De]||null;e:switch(e=t.stateNode,t.type){case"input":if(yt(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+xt(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var l=r[De]||null;if(!l)throw Error(a(90));yt(r,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name)}}for(t=0;t<n.length;t++)(r=n[t]).form===e.form&&gt(r)}break e;case"textarea":Nt(e,n.value,n.defaultValue);break e;case"select":null!=(t=n.value)&&kt(e,!!n.multiple,t,!1)}}}var It=!1;function $t(e,t,n){if(It)return e(t,n);It=!0;try{return e(t)}finally{if(It=!1,(null!==Rt||null!==Dt)&&(qu(),Rt&&(t=Rt,e=Dt,Dt=Rt=null,At(t),e)))for(t=0;t<e.length;t++)At(e[t])}}function Ut(e,t){var n=e.stateNode;if(null===n)return null;var r=n[De]||null;if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!=typeof n)throw Error(a(231,t,typeof n));return n}var Vt=!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement),Bt=!1;if(Vt)try{var Ht={};Object.defineProperty(Ht,"passive",{get:function(){Bt=!0}}),window.addEventListener("test",Ht,Ht),window.removeEventListener("test",Ht,Ht)}catch(Af){Bt=!1}var qt=null,Wt=null,Qt=null;function Zt(){if(Qt)return Qt;var e,t,n=Wt,a=n.length,r="value"in qt?qt.value:qt.textContent,l=r.length;for(e=0;e<a&&n[e]===r[e];e++);var s=a-e;for(t=1;t<=s&&n[a-t]===r[l-t];t++);return Qt=r.slice(e,1<t?1-t:void 0)}function Gt(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function Yt(){return!0}function Kt(){return!1}function Xt(e){function t(t,n,a,r,l){for(var s in this._reactName=t,this._targetInst=a,this.type=n,this.nativeEvent=r,this.target=l,this.currentTarget=null,e)e.hasOwnProperty(s)&&(t=e[s],this[s]=t?t(r):r[s]);return this.isDefaultPrevented=(null!=r.defaultPrevented?r.defaultPrevented:!1===r.returnValue)?Yt:Kt,this.isPropagationStopped=Kt,this}return u(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!=typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=Yt)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!=typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=Yt)},persist:function(){},isPersistent:Yt}),t}var Jt,en,tn,nn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},an=Xt(nn),rn=u({},nn,{view:0,detail:0}),ln=Xt(rn),sn=u({},rn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:vn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==tn&&(tn&&"mousemove"===e.type?(Jt=e.screenX-tn.screenX,en=e.screenY-tn.screenY):en=Jt=0,tn=e),Jt)},movementY:function(e){return"movementY"in e?e.movementY:en}}),on=Xt(sn),un=Xt(u({},sn,{dataTransfer:0})),cn=Xt(u({},rn,{relatedTarget:0})),dn=Xt(u({},nn,{animationName:0,elapsedTime:0,pseudoElement:0})),fn=Xt(u({},nn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}})),mn=Xt(u({},nn,{data:0})),pn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},hn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},gn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function bn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=gn[e])&&!!t[e]}function vn(){return bn}var xn=Xt(u({},rn,{key:function(e){if(e.key){var t=pn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=Gt(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?hn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:vn,charCode:function(e){return"keypress"===e.type?Gt(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?Gt(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}})),yn=Xt(u({},sn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),wn=Xt(u({},rn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:vn})),Sn=Xt(u({},nn,{propertyName:0,elapsedTime:0,pseudoElement:0})),kn=Xt(u({},sn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0})),Nn=Xt(u({},nn,{newState:0,oldState:0})),jn=[9,13,27,32],Cn=Vt&&"CompositionEvent"in window,En=null;Vt&&"documentMode"in document&&(En=document.documentMode);var Tn=Vt&&"TextEvent"in window&&!En,Pn=Vt&&(!Cn||En&&8<En&&11>=En),zn=String.fromCharCode(32),Ln=!1;function _n(e,t){switch(e){case"keyup":return-1!==jn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Mn(e){return"object"==typeof(e=e.detail)&&"data"in e?e.data:null}var On=!1;var Fn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Rn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Fn[e.type]:"textarea"===t}function Dn(e,t,n,a){Rt?Dt?Dt.push(a):Dt=[a]:Rt=a,0<(t=Zc(t,"onChange")).length&&(n=new an("onChange","change",null,n,a),e.push({event:n,listeners:t}))}var An=null,In=null;function $n(e){$c(e,0)}function Un(e){if(gt(Qe(e)))return e}function Vn(e,t){if("change"===e)return t}var Bn=!1;if(Vt){var Hn;if(Vt){var qn="oninput"in document;if(!qn){var Wn=document.createElement("div");Wn.setAttribute("oninput","return;"),qn="function"==typeof Wn.oninput}Hn=qn}else Hn=!1;Bn=Hn&&(!document.documentMode||9<document.documentMode)}function Qn(){An&&(An.detachEvent("onpropertychange",Zn),In=An=null)}function Zn(e){if("value"===e.propertyName&&Un(In)){var t=[];Dn(t,In,e,Ft(e)),$t($n,t)}}function Gn(e,t,n){"focusin"===e?(Qn(),In=n,(An=t).attachEvent("onpropertychange",Zn)):"focusout"===e&&Qn()}function Yn(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Un(In)}function Kn(e,t){if("click"===e)return Un(t)}function Xn(e,t){if("input"===e||"change"===e)return Un(t)}var Jn="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t};function ea(e,t){if(Jn(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var n=Object.keys(e),a=Object.keys(t);if(n.length!==a.length)return!1;for(a=0;a<n.length;a++){var r=n[a];if(!ee.call(t,r)||!Jn(e[r],t[r]))return!1}return!0}function ta(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function na(e,t){var n,a=ta(e);for(e=0;a;){if(3===a.nodeType){if(n=e+a.textContent.length,e<=t&&n>=t)return{node:a,offset:t-e};e=n}e:{for(;a;){if(a.nextSibling){a=a.nextSibling;break e}a=a.parentNode}a=void 0}a=ta(a)}}function aa(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?aa(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function ra(e){for(var t=bt((e=null!=e&&null!=e.ownerDocument&&null!=e.ownerDocument.defaultView?e.ownerDocument.defaultView:window).document);t instanceof e.HTMLIFrameElement;){try{var n="string"==typeof t.contentWindow.location.href}catch(a){n=!1}if(!n)break;t=bt((e=t.contentWindow).document)}return t}function la(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}var sa=Vt&&"documentMode"in document&&11>=document.documentMode,ia=null,oa=null,ua=null,ca=!1;function da(e,t,n){var a=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;ca||null==ia||ia!==bt(a)||("selectionStart"in(a=ia)&&la(a)?a={start:a.selectionStart,end:a.selectionEnd}:a={anchorNode:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset},ua&&ea(ua,a)||(ua=a,0<(a=Zc(oa,"onSelect")).length&&(t=new an("onSelect","select",null,t,n),e.push({event:t,listeners:a}),t.target=ia)))}function fa(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var ma={animationend:fa("Animation","AnimationEnd"),animationiteration:fa("Animation","AnimationIteration"),animationstart:fa("Animation","AnimationStart"),transitionrun:fa("Transition","TransitionRun"),transitionstart:fa("Transition","TransitionStart"),transitioncancel:fa("Transition","TransitionCancel"),transitionend:fa("Transition","TransitionEnd")},pa={},ha={};function ga(e){if(pa[e])return pa[e];if(!ma[e])return e;var t,n=ma[e];for(t in n)if(n.hasOwnProperty(t)&&t in ha)return pa[e]=n[t];return e}Vt&&(ha=document.createElement("div").style,"AnimationEvent"in window||(delete ma.animationend.animation,delete ma.animationiteration.animation,delete ma.animationstart.animation),"TransitionEvent"in window||delete ma.transitionend.transition);var ba=ga("animationend"),va=ga("animationiteration"),xa=ga("animationstart"),ya=ga("transitionrun"),wa=ga("transitionstart"),Sa=ga("transitioncancel"),ka=ga("transitionend"),Na=new Map,ja="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Ca(e,t){Na.set(e,t),Xe(t,[e])}ja.push("scrollEnd");var Ea=new WeakMap;function Ta(e,t){if("object"==typeof e&&null!==e){var n=Ea.get(e);return void 0!==n?n:(t={value:e,source:t,stack:ft(t)},Ea.set(e,t),t)}return{value:e,source:t,stack:ft(t)}}var Pa=[],za=0,La=0;function _a(){for(var e=za,t=La=za=0;t<e;){var n=Pa[t];Pa[t++]=null;var a=Pa[t];Pa[t++]=null;var r=Pa[t];Pa[t++]=null;var l=Pa[t];if(Pa[t++]=null,null!==a&&null!==r){var s=a.pending;null===s?r.next=r:(r.next=s.next,s.next=r),a.pending=r}0!==l&&Ra(n,r,l)}}function Ma(e,t,n,a){Pa[za++]=e,Pa[za++]=t,Pa[za++]=n,Pa[za++]=a,La|=a,e.lanes|=a,null!==(e=e.alternate)&&(e.lanes|=a)}function Oa(e,t,n,a){return Ma(e,t,n,a),Da(e)}function Fa(e,t){return Ma(e,null,null,t),Da(e)}function Ra(e,t,n){e.lanes|=n;var a=e.alternate;null!==a&&(a.lanes|=n);for(var r=!1,l=e.return;null!==l;)l.childLanes|=n,null!==(a=l.alternate)&&(a.childLanes|=n),22===l.tag&&(null===(e=l.stateNode)||1&e._visibility||(r=!0)),e=l,l=l.return;return 3===e.tag?(l=e.stateNode,r&&null!==t&&(r=31-be(n),null===(a=(e=l.hiddenUpdates)[r])?e[r]=[t]:a.push(t),t.lane=536870912|n),l):null}function Da(e){if(50<Ru)throw Ru=0,Du=null,Error(a(185));for(var t=e.return;null!==t;)t=(e=t).return;return 3===e.tag?e.stateNode:null}var Aa={};function Ia(e,t,n,a){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function $a(e,t,n,a){return new Ia(e,t,n,a)}function Ua(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Va(e,t){var n=e.alternate;return null===n?((n=$a(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=65011712&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function Ba(e,t){e.flags&=65011714;var n=e.alternate;return null===n?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Ha(e,t,n,r,l,s){var i=0;if(r=e,"function"==typeof e)Ua(e)&&(i=1);else if("string"==typeof e)i=function(e,t,n){if(1===n||null!=t.itemProp)return!1;switch(e){case"meta":case"title":return!0;case"style":if("string"!=typeof t.precedence||"string"!=typeof t.href||""===t.href)break;return!0;case"link":if("string"!=typeof t.rel||"string"!=typeof t.href||""===t.href||t.onLoad||t.onError)break;return"stylesheet"!==t.rel||(e=t.disabled,"string"==typeof t.precedence&&null==e);case"script":if(t.async&&"function"!=typeof t.async&&"symbol"!=typeof t.async&&!t.onLoad&&!t.onError&&t.src&&"string"==typeof t.src)return!0}return!1}(e,n,W.current)?26:"html"===e||"head"===e||"body"===e?27:5;else e:switch(e){case L:return(e=$a(31,n,t,l)).elementType=L,e.lanes=s,e;case p:return qa(n.children,l,s,t);case h:i=8,l|=24;break;case g:return(e=$a(12,n,t,2|l)).elementType=g,e.lanes=s,e;case C:return(e=$a(13,n,t,l)).elementType=C,e.lanes=s,e;case T:return(e=$a(19,n,t,l)).elementType=T,e.lanes=s,e;default:if("object"==typeof e&&null!==e)switch(e.$$typeof){case v:case w:i=10;break e;case x:i=9;break e;case N:i=11;break e;case P:i=14;break e;case z:i=16,r=null;break e}i=29,n=Error(a(130,null===e?"null":typeof e,"")),r=null}return(t=$a(i,n,t,l)).elementType=e,t.type=r,t.lanes=s,t}function qa(e,t,n,a){return(e=$a(7,e,a,t)).lanes=n,e}function Wa(e,t,n){return(e=$a(6,e,null,t)).lanes=n,e}function Qa(e,t,n){return(t=$a(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Za=[],Ga=0,Ya=null,Ka=0,Xa=[],Ja=0,er=null,tr=1,nr="";function ar(e,t){Za[Ga++]=Ka,Za[Ga++]=Ya,Ya=e,Ka=t}function rr(e,t,n){Xa[Ja++]=tr,Xa[Ja++]=nr,Xa[Ja++]=er,er=e;var a=tr;e=nr;var r=32-be(a)-1;a&=~(1<<r),n+=1;var l=32-be(t)+r;if(30<l){var s=r-r%5;l=(a&(1<<s)-1).toString(32),a>>=s,r-=s,tr=1<<32-be(t)+r|n<<r|a,nr=l+e}else tr=1<<l|n<<r|a,nr=e}function lr(e){null!==e.return&&(ar(e,1),rr(e,1,0))}function sr(e){for(;e===Ya;)Ya=Za[--Ga],Za[Ga]=null,Ka=Za[--Ga],Za[Ga]=null;for(;e===er;)er=Xa[--Ja],Xa[Ja]=null,nr=Xa[--Ja],Xa[Ja]=null,tr=Xa[--Ja],Xa[Ja]=null}var ir=null,or=null,ur=!1,cr=null,dr=!1,fr=Error(a(519));function mr(e){throw xr(Ta(Error(a(418,"")),e)),fr}function pr(e){var t=e.stateNode,n=e.type,a=e.memoizedProps;switch(t[Re]=e,t[De]=a,n){case"dialog":Uc("cancel",t),Uc("close",t);break;case"iframe":case"object":case"embed":Uc("load",t);break;case"video":case"audio":for(n=0;n<Ac.length;n++)Uc(Ac[n],t);break;case"source":Uc("error",t);break;case"img":case"image":case"link":Uc("error",t),Uc("load",t);break;case"details":Uc("toggle",t);break;case"input":Uc("invalid",t),wt(t,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),ht(t);break;case"select":Uc("invalid",t);break;case"textarea":Uc("invalid",t),jt(t,a.value,a.defaultValue,a.children),ht(t)}"string"!=typeof(n=a.children)&&"number"!=typeof n&&"bigint"!=typeof n||t.textContent===""+n||!0===a.suppressHydrationWarning||ed(t.textContent,n)?(null!=a.popover&&(Uc("beforetoggle",t),Uc("toggle",t)),null!=a.onScroll&&Uc("scroll",t),null!=a.onScrollEnd&&Uc("scrollend",t),null!=a.onClick&&(t.onclick=td),t=!0):t=!1,t||mr(e)}function hr(e){for(ir=e.return;ir;)switch(ir.tag){case 5:case 13:return void(dr=!1);case 27:case 3:return void(dr=!0);default:ir=ir.return}}function gr(e){if(e!==ir)return!1;if(!ur)return hr(e),ur=!0,!1;var t,n=e.tag;if((t=3!==n&&27!==n)&&((t=5===n)&&(t=!("form"!==(t=e.type)&&"button"!==t)||cd(e.type,e.memoizedProps)),t=!t),t&&or&&mr(e),hr(e),13===n){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(a(317));e:{for(e=e.nextSibling,n=0;e;){if(8===e.nodeType)if("/$"===(t=e.data)){if(0===n){or=wd(e.nextSibling);break e}n--}else"$"!==t&&"$!"!==t&&"$?"!==t||n++;e=e.nextSibling}or=null}}else 27===n?(n=or,bd(e.type)?(e=Sd,Sd=null,or=e):or=n):or=ir?wd(e.stateNode.nextSibling):null;return!0}function br(){or=ir=null,ur=!1}function vr(){var e=cr;return null!==e&&(null===ku?ku=e:ku.push.apply(ku,e),cr=null),e}function xr(e){null===cr?cr=[e]:cr.push(e)}var yr=B(null),wr=null,Sr=null;function kr(e,t,n){q(yr,t._currentValue),t._currentValue=n}function Nr(e){e._currentValue=yr.current,H(yr)}function jr(e,t,n){for(;null!==e;){var a=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==a&&(a.childLanes|=t)):null!==a&&(a.childLanes&t)!==t&&(a.childLanes|=t),e===n)break;e=e.return}}function Cr(e,t,n,r){var l=e.child;for(null!==l&&(l.return=e);null!==l;){var s=l.dependencies;if(null!==s){var i=l.child;s=s.firstContext;e:for(;null!==s;){var o=s;s=l;for(var u=0;u<t.length;u++)if(o.context===t[u]){s.lanes|=n,null!==(o=s.alternate)&&(o.lanes|=n),jr(s.return,n,e),r||(i=null);break e}s=o.next}}else if(18===l.tag){if(null===(i=l.return))throw Error(a(341));i.lanes|=n,null!==(s=i.alternate)&&(s.lanes|=n),jr(i,n,e),i=null}else i=l.child;if(null!==i)i.return=l;else for(i=l;null!==i;){if(i===e){i=null;break}if(null!==(l=i.sibling)){l.return=i.return,i=l;break}i=i.return}l=i}}function Er(e,t,n,r){e=null;for(var l=t,s=!1;null!==l;){if(!s)if(524288&l.flags)s=!0;else if(262144&l.flags)break;if(10===l.tag){var i=l.alternate;if(null===i)throw Error(a(387));if(null!==(i=i.memoizedProps)){var o=l.type;Jn(l.pendingProps.value,i.value)||(null!==e?e.push(o):e=[o])}}else if(l===G.current){if(null===(i=l.alternate))throw Error(a(387));i.memoizedState.memoizedState!==l.memoizedState.memoizedState&&(null!==e?e.push(Xd):e=[Xd])}l=l.return}null!==e&&Cr(t,e,n,r),t.flags|=262144}function Tr(e){for(e=e.firstContext;null!==e;){if(!Jn(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Pr(e){wr=e,Sr=null,null!==(e=e.dependencies)&&(e.firstContext=null)}function zr(e){return _r(wr,e)}function Lr(e,t){return null===wr&&Pr(e),_r(e,t)}function _r(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},null===Sr){if(null===e)throw Error(a(308));Sr=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else Sr=Sr.next=t;return n}var Mr="undefined"!=typeof AbortController?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(t,n){e.push(n)}};this.abort=function(){t.aborted=!0,e.forEach((function(e){return e()}))}},Or=e.unstable_scheduleCallback,Fr=e.unstable_NormalPriority,Rr={$$typeof:w,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Dr(){return{controller:new Mr,data:new Map,refCount:0}}function Ar(e){e.refCount--,0===e.refCount&&Or(Fr,(function(){e.controller.abort()}))}var Ir=null,$r=0,Ur=0,Vr=null;function Br(){if(0===--$r&&null!==Ir){null!==Vr&&(Vr.status="fulfilled");var e=Ir;Ir=null,Ur=0,Vr=null;for(var t=0;t<e.length;t++)(0,e[t])()}}var Hr=A.S;A.S=function(e,t){"object"==typeof t&&null!==t&&"function"==typeof t.then&&function(e,t){if(null===Ir){var n=Ir=[];$r=0,Ur=Mc(),Vr={status:"pending",value:void 0,then:function(e){n.push(e)}}}$r++,t.then(Br,Br)}(0,t),null!==Hr&&Hr(e,t)};var qr=B(null);function Wr(){var e=qr.current;return null!==e?e:iu.pooledCache}function Qr(e,t){q(qr,null===t?qr.current:t.pool)}function Zr(){var e=Wr();return null===e?null:{parent:Rr._currentValue,pool:e}}var Gr=Error(a(460)),Yr=Error(a(474)),Kr=Error(a(542)),Xr={then:function(){}};function Jr(e){return"fulfilled"===(e=e.status)||"rejected"===e}function el(){}function tl(e,t,n){switch(void 0===(n=e[n])?e.push(t):n!==t&&(t.then(el,el),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw rl(e=t.reason),e;default:if("string"==typeof t.status)t.then(el,el);else{if(null!==(e=iu)&&100<e.shellSuspendCounter)throw Error(a(482));(e=t).status="pending",e.then((function(e){if("pending"===t.status){var n=t;n.status="fulfilled",n.value=e}}),(function(e){if("pending"===t.status){var n=t;n.status="rejected",n.reason=e}}))}switch(t.status){case"fulfilled":return t.value;case"rejected":throw rl(e=t.reason),e}throw nl=t,Gr}}var nl=null;function al(){if(null===nl)throw Error(a(459));var e=nl;return nl=null,e}function rl(e){if(e===Gr||e===Kr)throw Error(a(483))}var ll=!1;function sl(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function il(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function ol(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function ul(e,t,n){var a=e.updateQueue;if(null===a)return null;if(a=a.shared,2&su){var r=a.pending;return null===r?t.next=t:(t.next=r.next,r.next=t),a.pending=t,t=Da(e),Ra(e,null,n),t}return Ma(e,a,t,n),Da(e)}function cl(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,4194048&n)){var a=t.lanes;n|=a&=e.pendingLanes,t.lanes=n,Le(e,n)}}function dl(e,t){var n=e.updateQueue,a=e.alternate;if(null!==a&&n===(a=a.updateQueue)){var r=null,l=null;if(null!==(n=n.firstBaseUpdate)){do{var s={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};null===l?r=l=s:l=l.next=s,n=n.next}while(null!==n);null===l?r=l=t:l=l.next=t}else r=l=t;return n={baseState:a.baseState,firstBaseUpdate:r,lastBaseUpdate:l,shared:a.shared,callbacks:a.callbacks},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var fl=!1;function ml(){if(fl){if(null!==Vr)throw Vr}}function pl(e,t,n,a){fl=!1;var r=e.updateQueue;ll=!1;var l=r.firstBaseUpdate,s=r.lastBaseUpdate,i=r.shared.pending;if(null!==i){r.shared.pending=null;var o=i,c=o.next;o.next=null,null===s?l=c:s.next=c,s=o;var d=e.alternate;null!==d&&((i=(d=d.updateQueue).lastBaseUpdate)!==s&&(null===i?d.firstBaseUpdate=c:i.next=c,d.lastBaseUpdate=o))}if(null!==l){var f=r.baseState;for(s=0,d=c=o=null,i=l;;){var m=-536870913&i.lane,p=m!==i.lane;if(p?(uu&m)===m:(a&m)===m){0!==m&&m===Ur&&(fl=!0),null!==d&&(d=d.next={lane:0,tag:i.tag,payload:i.payload,callback:null,next:null});e:{var h=e,g=i;m=t;var b=n;switch(g.tag){case 1:if("function"==typeof(h=g.payload)){f=h.call(b,f,m);break e}f=h;break e;case 3:h.flags=-65537&h.flags|128;case 0:if(null==(m="function"==typeof(h=g.payload)?h.call(b,f,m):h))break e;f=u({},f,m);break e;case 2:ll=!0}}null!==(m=i.callback)&&(e.flags|=64,p&&(e.flags|=8192),null===(p=r.callbacks)?r.callbacks=[m]:p.push(m))}else p={lane:m,tag:i.tag,payload:i.payload,callback:i.callback,next:null},null===d?(c=d=p,o=f):d=d.next=p,s|=m;if(null===(i=i.next)){if(null===(i=r.shared.pending))break;i=(p=i).next,p.next=null,r.lastBaseUpdate=p,r.shared.pending=null}}null===d&&(o=f),r.baseState=o,r.firstBaseUpdate=c,r.lastBaseUpdate=d,null===l&&(r.shared.lanes=0),bu|=s,e.lanes=s,e.memoizedState=f}}function hl(e,t){if("function"!=typeof e)throw Error(a(191,e));e.call(t)}function gl(e,t){var n=e.callbacks;if(null!==n)for(e.callbacks=null,e=0;e<n.length;e++)hl(n[e],t)}var bl=B(null),vl=B(0);function xl(e,t){q(vl,e=hu),q(bl,t),hu=e|t.baseLanes}function yl(){q(vl,hu),q(bl,bl.current)}function wl(){hu=vl.current,H(bl),H(vl)}var Sl=0,kl=null,Nl=null,jl=null,Cl=!1,El=!1,Tl=!1,Pl=0,zl=0,Ll=null,_l=0;function Ml(){throw Error(a(321))}function Ol(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Jn(e[n],t[n]))return!1;return!0}function Fl(e,t,n,a,r,l){return Sl=l,kl=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,A.H=null===e||null===e.memoizedState?Gs:Ys,Tl=!1,l=n(a,r),Tl=!1,El&&(l=Dl(t,n,a,r)),Rl(e),l}function Rl(e){A.H=Zs;var t=null!==Nl&&null!==Nl.next;if(Sl=0,jl=Nl=kl=null,Cl=!1,zl=0,Ll=null,t)throw Error(a(300));null===e||zi||null!==(e=e.dependencies)&&Tr(e)&&(zi=!0)}function Dl(e,t,n,r){kl=e;var l=0;do{if(El&&(Ll=null),zl=0,El=!1,25<=l)throw Error(a(301));if(l+=1,jl=Nl=null,null!=e.updateQueue){var s=e.updateQueue;s.lastEffect=null,s.events=null,s.stores=null,null!=s.memoCache&&(s.memoCache.index=0)}A.H=Ks,s=t(n,r)}while(El);return s}function Al(){var e=A.H,t=e.useState()[0];return t="function"==typeof t.then?Hl(t):t,e=e.useState()[0],(null!==Nl?Nl.memoizedState:null)!==e&&(kl.flags|=1024),t}function Il(){var e=0!==Pl;return Pl=0,e}function $l(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function Ul(e){if(Cl){for(e=e.memoizedState;null!==e;){var t=e.queue;null!==t&&(t.pending=null),e=e.next}Cl=!1}Sl=0,jl=Nl=kl=null,El=!1,zl=Pl=0,Ll=null}function Vl(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===jl?kl.memoizedState=jl=e:jl=jl.next=e,jl}function Bl(){if(null===Nl){var e=kl.alternate;e=null!==e?e.memoizedState:null}else e=Nl.next;var t=null===jl?kl.memoizedState:jl.next;if(null!==t)jl=t,Nl=e;else{if(null===e){if(null===kl.alternate)throw Error(a(467));throw Error(a(310))}e={memoizedState:(Nl=e).memoizedState,baseState:Nl.baseState,baseQueue:Nl.baseQueue,queue:Nl.queue,next:null},null===jl?kl.memoizedState=jl=e:jl=jl.next=e}return jl}function Hl(e){var t=zl;return zl+=1,null===Ll&&(Ll=[]),e=tl(Ll,e,t),t=kl,null===(null===jl?t.memoizedState:jl.next)&&(t=t.alternate,A.H=null===t||null===t.memoizedState?Gs:Ys),e}function ql(e){if(null!==e&&"object"==typeof e){if("function"==typeof e.then)return Hl(e);if(e.$$typeof===w)return zr(e)}throw Error(a(438,String(e)))}function Wl(e){var t=null,n=kl.updateQueue;if(null!==n&&(t=n.memoCache),null==t){var a=kl.alternate;null!==a&&(null!==(a=a.updateQueue)&&(null!=(a=a.memoCache)&&(t={data:a.data.map((function(e){return e.slice()})),index:0})))}if(null==t&&(t={data:[],index:0}),null===n&&(n={lastEffect:null,events:null,stores:null,memoCache:null},kl.updateQueue=n),n.memoCache=t,void 0===(n=t.data[t.index]))for(n=t.data[t.index]=Array(e),a=0;a<e;a++)n[a]=_;return t.index++,n}function Ql(e,t){return"function"==typeof t?t(e):t}function Zl(e){return Gl(Bl(),Nl,e)}function Gl(e,t,n){var r=e.queue;if(null===r)throw Error(a(311));r.lastRenderedReducer=n;var l=e.baseQueue,s=r.pending;if(null!==s){if(null!==l){var i=l.next;l.next=s.next,s.next=i}t.baseQueue=l=s,r.pending=null}if(s=e.baseState,null===l)e.memoizedState=s;else{var o=i=null,u=null,c=t=l.next,d=!1;do{var f=-536870913&c.lane;if(f!==c.lane?(uu&f)===f:(Sl&f)===f){var m=c.revertLane;if(0===m)null!==u&&(u=u.next={lane:0,revertLane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),f===Ur&&(d=!0);else{if((Sl&m)===m){c=c.next,m===Ur&&(d=!0);continue}f={lane:0,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},null===u?(o=u=f,i=s):u=u.next=f,kl.lanes|=m,bu|=m}f=c.action,Tl&&n(s,f),s=c.hasEagerState?c.eagerState:n(s,f)}else m={lane:f,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},null===u?(o=u=m,i=s):u=u.next=m,kl.lanes|=f,bu|=f;c=c.next}while(null!==c&&c!==t);if(null===u?i=s:u.next=o,!Jn(s,e.memoizedState)&&(zi=!0,d&&null!==(n=Vr)))throw n;e.memoizedState=s,e.baseState=i,e.baseQueue=u,r.lastRenderedState=s}return null===l&&(r.lanes=0),[e.memoizedState,r.dispatch]}function Yl(e){var t=Bl(),n=t.queue;if(null===n)throw Error(a(311));n.lastRenderedReducer=e;var r=n.dispatch,l=n.pending,s=t.memoizedState;if(null!==l){n.pending=null;var i=l=l.next;do{s=e(s,i.action),i=i.next}while(i!==l);Jn(s,t.memoizedState)||(zi=!0),t.memoizedState=s,null===t.baseQueue&&(t.baseState=s),n.lastRenderedState=s}return[s,r]}function Kl(e,t,n){var r=kl,l=Bl(),s=ur;if(s){if(void 0===n)throw Error(a(407));n=n()}else n=t();var i=!Jn((Nl||l).memoizedState,n);if(i&&(l.memoizedState=n,zi=!0),l=l.queue,ys(2048,8,es.bind(null,r,l,e),[e]),l.getSnapshot!==t||i||null!==jl&&1&jl.memoizedState.tag){if(r.flags|=2048,bs(9,{destroy:void 0,resource:void 0},Jl.bind(null,r,l,n,t),null),null===iu)throw Error(a(349));s||124&Sl||Xl(r,t,n)}return n}function Xl(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=kl.updateQueue)?(t={lastEffect:null,events:null,stores:null,memoCache:null},kl.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Jl(e,t,n,a){t.value=n,t.getSnapshot=a,ts(t)&&ns(e)}function es(e,t,n){return n((function(){ts(t)&&ns(e)}))}function ts(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Jn(e,n)}catch(a){return!0}}function ns(e){var t=Fa(e,2);null!==t&&$u(t,e,2)}function as(e){var t=Vl();if("function"==typeof e){var n=e;if(e=n(),Tl){ge(!0);try{n()}finally{ge(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ql,lastRenderedState:e},t}function rs(e,t,n,a){return e.baseState=n,Gl(e,Nl,"function"==typeof a?a:Ql)}function ls(e,t,n,r,l){if(qs(e))throw Error(a(485));if(null!==(e=t.action)){var s={payload:l,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(e){s.listeners.push(e)}};null!==A.T?n(!0):s.isTransition=!1,r(s),null===(n=t.pending)?(s.next=t.pending=s,ss(t,s)):(s.next=n.next,t.pending=n.next=s)}}function ss(e,t){var n=t.action,a=t.payload,r=e.state;if(t.isTransition){var l=A.T,s={};A.T=s;try{var i=n(r,a),o=A.S;null!==o&&o(s,i),is(e,t,i)}catch(u){us(e,t,u)}finally{A.T=l}}else try{is(e,t,l=n(r,a))}catch(c){us(e,t,c)}}function is(e,t,n){null!==n&&"object"==typeof n&&"function"==typeof n.then?n.then((function(n){os(e,t,n)}),(function(n){return us(e,t,n)})):os(e,t,n)}function os(e,t,n){t.status="fulfilled",t.value=n,cs(t),e.state=n,null!==(t=e.pending)&&((n=t.next)===t?e.pending=null:(n=n.next,t.next=n,ss(e,n)))}function us(e,t,n){var a=e.pending;if(e.pending=null,null!==a){a=a.next;do{t.status="rejected",t.reason=n,cs(t),t=t.next}while(t!==a)}e.action=null}function cs(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function ds(e,t){return t}function fs(e,t){if(ur){var n=iu.formState;if(null!==n){e:{var a=kl;if(ur){if(or){t:{for(var r=or,l=dr;8!==r.nodeType;){if(!l){r=null;break t}if(null===(r=wd(r.nextSibling))){r=null;break t}}r="F!"===(l=r.data)||"F"===l?r:null}if(r){or=wd(r.nextSibling),a="F!"===r.data;break e}}mr(a)}a=!1}a&&(t=n[0])}}return(n=Vl()).memoizedState=n.baseState=t,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:ds,lastRenderedState:t},n.queue=a,n=Vs.bind(null,kl,a),a.dispatch=n,a=as(!1),l=Hs.bind(null,kl,!1,a.queue),r={state:t,dispatch:null,action:e,pending:null},(a=Vl()).queue=r,n=ls.bind(null,kl,r,l,n),r.dispatch=n,a.memoizedState=e,[t,n,!1]}function ms(e){return ps(Bl(),Nl,e)}function ps(e,t,n){if(t=Gl(e,t,ds)[0],e=Zl(Ql)[0],"object"==typeof t&&null!==t&&"function"==typeof t.then)try{var a=Hl(t)}catch(s){if(s===Gr)throw Kr;throw s}else a=t;var r=(t=Bl()).queue,l=r.dispatch;return n!==t.memoizedState&&(kl.flags|=2048,bs(9,{destroy:void 0,resource:void 0},hs.bind(null,r,n),null)),[a,l,e]}function hs(e,t){e.action=t}function gs(e){var t=Bl(),n=Nl;if(null!==n)return ps(t,n,e);Bl(),t=t.memoizedState;var a=(n=Bl()).queue.dispatch;return n.memoizedState=e,[t,a,!1]}function bs(e,t,n,a){return e={tag:e,create:n,deps:a,inst:t,next:null},null===(t=kl.updateQueue)&&(t={lastEffect:null,events:null,stores:null,memoCache:null},kl.updateQueue=t),null===(n=t.lastEffect)?t.lastEffect=e.next=e:(a=n.next,n.next=e,e.next=a,t.lastEffect=e),e}function vs(){return Bl().memoizedState}function xs(e,t,n,a){var r=Vl();a=void 0===a?null:a,kl.flags|=e,r.memoizedState=bs(1|t,{destroy:void 0,resource:void 0},n,a)}function ys(e,t,n,a){var r=Bl();a=void 0===a?null:a;var l=r.memoizedState.inst;null!==Nl&&null!==a&&Ol(a,Nl.memoizedState.deps)?r.memoizedState=bs(t,l,n,a):(kl.flags|=e,r.memoizedState=bs(1|t,l,n,a))}function ws(e,t){xs(8390656,8,e,t)}function Ss(e,t){ys(2048,8,e,t)}function ks(e,t){return ys(4,2,e,t)}function Ns(e,t){return ys(4,4,e,t)}function js(e,t){if("function"==typeof t){e=e();var n=t(e);return function(){"function"==typeof n?n():t(null)}}if(null!=t)return e=e(),t.current=e,function(){t.current=null}}function Cs(e,t,n){n=null!=n?n.concat([e]):null,ys(4,4,js.bind(null,t,e),n)}function Es(){}function Ts(e,t){var n=Bl();t=void 0===t?null:t;var a=n.memoizedState;return null!==t&&Ol(t,a[1])?a[0]:(n.memoizedState=[e,t],e)}function Ps(e,t){var n=Bl();t=void 0===t?null:t;var a=n.memoizedState;if(null!==t&&Ol(t,a[1]))return a[0];if(a=e(),Tl){ge(!0);try{e()}finally{ge(!1)}}return n.memoizedState=[a,t],a}function zs(e,t,n){return void 0===n||1073741824&Sl?e.memoizedState=t:(e.memoizedState=n,e=Iu(),kl.lanes|=e,bu|=e,n)}function Ls(e,t,n,a){return Jn(n,t)?n:null!==bl.current?(e=zs(e,n,a),Jn(e,t)||(zi=!0),e):42&Sl?(e=Iu(),kl.lanes|=e,bu|=e,t):(zi=!0,e.memoizedState=n)}function _s(e,t,n,a,r){var l=I.p;I.p=0!==l&&8>l?l:8;var s,i,o,u=A.T,c={};A.T=c,Hs(e,!1,t,n);try{var d=r(),f=A.S;if(null!==f&&f(c,d),null!==d&&"object"==typeof d&&"function"==typeof d.then)Bs(e,t,(s=a,i=[],o={status:"pending",value:null,reason:null,then:function(e){i.push(e)}},d.then((function(){o.status="fulfilled",o.value=s;for(var e=0;e<i.length;e++)(0,i[e])(s)}),(function(e){for(o.status="rejected",o.reason=e,e=0;e<i.length;e++)(0,i[e])(void 0)})),o),Au());else Bs(e,t,a,Au())}catch(m){Bs(e,t,{then:function(){},status:"rejected",reason:m},Au())}finally{I.p=l,A.T=u}}function Ms(){}function Os(e,t,n,r){if(5!==e.tag)throw Error(a(476));var l=Fs(e).queue;_s(e,l,t,$,null===n?Ms:function(){return Rs(e),n(r)})}function Fs(e){var t=e.memoizedState;if(null!==t)return t;var n={};return(t={memoizedState:$,baseState:$,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ql,lastRenderedState:$},next:null}).next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ql,lastRenderedState:n},next:null},e.memoizedState=t,null!==(e=e.alternate)&&(e.memoizedState=t),t}function Rs(e){Bs(e,Fs(e).next.queue,{},Au())}function Ds(){return zr(Xd)}function As(){return Bl().memoizedState}function Is(){return Bl().memoizedState}function $s(e){for(var t=e.return;null!==t;){switch(t.tag){case 24:case 3:var n=Au(),a=ul(t,e=ol(n),n);return null!==a&&($u(a,t,n),cl(a,t,n)),t={cache:Dr()},void(e.payload=t)}t=t.return}}function Us(e,t,n){var a=Au();n={lane:a,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},qs(e)?Ws(t,n):null!==(n=Oa(e,t,n,a))&&($u(n,e,a),Qs(n,t,a))}function Vs(e,t,n){Bs(e,t,n,Au())}function Bs(e,t,n,a){var r={lane:a,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(qs(e))Ws(t,r);else{var l=e.alternate;if(0===e.lanes&&(null===l||0===l.lanes)&&null!==(l=t.lastRenderedReducer))try{var s=t.lastRenderedState,i=l(s,n);if(r.hasEagerState=!0,r.eagerState=i,Jn(i,s))return Ma(e,t,r,0),null===iu&&_a(),!1}catch(o){}if(null!==(n=Oa(e,t,r,a)))return $u(n,e,a),Qs(n,t,a),!0}return!1}function Hs(e,t,n,r){if(r={lane:2,revertLane:Mc(),action:r,hasEagerState:!1,eagerState:null,next:null},qs(e)){if(t)throw Error(a(479))}else null!==(t=Oa(e,n,r,2))&&$u(t,e,2)}function qs(e){var t=e.alternate;return e===kl||null!==t&&t===kl}function Ws(e,t){El=Cl=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Qs(e,t,n){if(4194048&n){var a=t.lanes;n|=a&=e.pendingLanes,t.lanes=n,Le(e,n)}}var Zs={readContext:zr,use:ql,useCallback:Ml,useContext:Ml,useEffect:Ml,useImperativeHandle:Ml,useLayoutEffect:Ml,useInsertionEffect:Ml,useMemo:Ml,useReducer:Ml,useRef:Ml,useState:Ml,useDebugValue:Ml,useDeferredValue:Ml,useTransition:Ml,useSyncExternalStore:Ml,useId:Ml,useHostTransitionStatus:Ml,useFormState:Ml,useActionState:Ml,useOptimistic:Ml,useMemoCache:Ml,useCacheRefresh:Ml},Gs={readContext:zr,use:ql,useCallback:function(e,t){return Vl().memoizedState=[e,void 0===t?null:t],e},useContext:zr,useEffect:ws,useImperativeHandle:function(e,t,n){n=null!=n?n.concat([e]):null,xs(4194308,4,js.bind(null,t,e),n)},useLayoutEffect:function(e,t){return xs(4194308,4,e,t)},useInsertionEffect:function(e,t){xs(4,2,e,t)},useMemo:function(e,t){var n=Vl();t=void 0===t?null:t;var a=e();if(Tl){ge(!0);try{e()}finally{ge(!1)}}return n.memoizedState=[a,t],a},useReducer:function(e,t,n){var a=Vl();if(void 0!==n){var r=n(t);if(Tl){ge(!0);try{n(t)}finally{ge(!1)}}}else r=t;return a.memoizedState=a.baseState=r,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:r},a.queue=e,e=e.dispatch=Us.bind(null,kl,e),[a.memoizedState,e]},useRef:function(e){return e={current:e},Vl().memoizedState=e},useState:function(e){var t=(e=as(e)).queue,n=Vs.bind(null,kl,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:Es,useDeferredValue:function(e,t){return zs(Vl(),e,t)},useTransition:function(){var e=as(!1);return e=_s.bind(null,kl,e.queue,!0,!1),Vl().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var r=kl,l=Vl();if(ur){if(void 0===n)throw Error(a(407));n=n()}else{if(n=t(),null===iu)throw Error(a(349));124&uu||Xl(r,t,n)}l.memoizedState=n;var s={value:n,getSnapshot:t};return l.queue=s,ws(es.bind(null,r,s,e),[e]),r.flags|=2048,bs(9,{destroy:void 0,resource:void 0},Jl.bind(null,r,s,n,t),null),n},useId:function(){var e=Vl(),t=iu.identifierPrefix;if(ur){var n=nr;t="«"+t+"R"+(n=(tr&~(1<<32-be(tr)-1)).toString(32)+n),0<(n=Pl++)&&(t+="H"+n.toString(32)),t+="»"}else t="«"+t+"r"+(n=_l++).toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:Ds,useFormState:fs,useActionState:fs,useOptimistic:function(e){var t=Vl();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=Hs.bind(null,kl,!0,n),n.dispatch=t,[e,t]},useMemoCache:Wl,useCacheRefresh:function(){return Vl().memoizedState=$s.bind(null,kl)}},Ys={readContext:zr,use:ql,useCallback:Ts,useContext:zr,useEffect:Ss,useImperativeHandle:Cs,useInsertionEffect:ks,useLayoutEffect:Ns,useMemo:Ps,useReducer:Zl,useRef:vs,useState:function(){return Zl(Ql)},useDebugValue:Es,useDeferredValue:function(e,t){return Ls(Bl(),Nl.memoizedState,e,t)},useTransition:function(){var e=Zl(Ql)[0],t=Bl().memoizedState;return["boolean"==typeof e?e:Hl(e),t]},useSyncExternalStore:Kl,useId:As,useHostTransitionStatus:Ds,useFormState:ms,useActionState:ms,useOptimistic:function(e,t){return rs(Bl(),0,e,t)},useMemoCache:Wl,useCacheRefresh:Is},Ks={readContext:zr,use:ql,useCallback:Ts,useContext:zr,useEffect:Ss,useImperativeHandle:Cs,useInsertionEffect:ks,useLayoutEffect:Ns,useMemo:Ps,useReducer:Yl,useRef:vs,useState:function(){return Yl(Ql)},useDebugValue:Es,useDeferredValue:function(e,t){var n=Bl();return null===Nl?zs(n,e,t):Ls(n,Nl.memoizedState,e,t)},useTransition:function(){var e=Yl(Ql)[0],t=Bl().memoizedState;return["boolean"==typeof e?e:Hl(e),t]},useSyncExternalStore:Kl,useId:As,useHostTransitionStatus:Ds,useFormState:gs,useActionState:gs,useOptimistic:function(e,t){var n=Bl();return null!==Nl?rs(n,0,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:Wl,useCacheRefresh:Is},Xs=null,Js=0;function ei(e){var t=Js;return Js+=1,null===Xs&&(Xs=[]),tl(Xs,e,t)}function ti(e,t){t=t.props.ref,e.ref=void 0!==t?t:null}function ni(e,t){if(t.$$typeof===c)throw Error(a(525));throw e=Object.prototype.toString.call(t),Error(a(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function ai(e){return(0,e._init)(e._payload)}function ri(e){function t(t,n){if(e){var a=t.deletions;null===a?(t.deletions=[n],t.flags|=16):a.push(n)}}function n(n,a){if(!e)return null;for(;null!==a;)t(n,a),a=a.sibling;return null}function r(e){for(var t=new Map;null!==e;)null!==e.key?t.set(e.key,e):t.set(e.index,e),e=e.sibling;return t}function l(e,t){return(e=Va(e,t)).index=0,e.sibling=null,e}function s(t,n,a){return t.index=a,e?null!==(a=t.alternate)?(a=a.index)<n?(t.flags|=67108866,n):a:(t.flags|=67108866,n):(t.flags|=1048576,n)}function i(t){return e&&null===t.alternate&&(t.flags|=67108866),t}function o(e,t,n,a){return null===t||6!==t.tag?((t=Wa(n,e.mode,a)).return=e,t):((t=l(t,n)).return=e,t)}function u(e,t,n,a){var r=n.type;return r===p?d(e,t,n.props.children,a,n.key):null!==t&&(t.elementType===r||"object"==typeof r&&null!==r&&r.$$typeof===z&&ai(r)===t.type)?(ti(t=l(t,n.props),n),t.return=e,t):(ti(t=Ha(n.type,n.key,n.props,null,e.mode,a),n),t.return=e,t)}function c(e,t,n,a){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Qa(n,e.mode,a)).return=e,t):((t=l(t,n.children||[])).return=e,t)}function d(e,t,n,a,r){return null===t||7!==t.tag?((t=qa(n,e.mode,a,r)).return=e,t):((t=l(t,n)).return=e,t)}function h(e,t,n){if("string"==typeof t&&""!==t||"number"==typeof t||"bigint"==typeof t)return(t=Wa(""+t,e.mode,n)).return=e,t;if("object"==typeof t&&null!==t){switch(t.$$typeof){case f:return ti(n=Ha(t.type,t.key,t.props,null,e.mode,n),t),n.return=e,n;case m:return(t=Qa(t,e.mode,n)).return=e,t;case z:return h(e,t=(0,t._init)(t._payload),n)}if(D(t)||O(t))return(t=qa(t,e.mode,n,null)).return=e,t;if("function"==typeof t.then)return h(e,ei(t),n);if(t.$$typeof===w)return h(e,Lr(e,t),n);ni(e,t)}return null}function g(e,t,n,a){var r=null!==t?t.key:null;if("string"==typeof n&&""!==n||"number"==typeof n||"bigint"==typeof n)return null!==r?null:o(e,t,""+n,a);if("object"==typeof n&&null!==n){switch(n.$$typeof){case f:return n.key===r?u(e,t,n,a):null;case m:return n.key===r?c(e,t,n,a):null;case z:return g(e,t,n=(r=n._init)(n._payload),a)}if(D(n)||O(n))return null!==r?null:d(e,t,n,a,null);if("function"==typeof n.then)return g(e,t,ei(n),a);if(n.$$typeof===w)return g(e,t,Lr(e,n),a);ni(e,n)}return null}function b(e,t,n,a,r){if("string"==typeof a&&""!==a||"number"==typeof a||"bigint"==typeof a)return o(t,e=e.get(n)||null,""+a,r);if("object"==typeof a&&null!==a){switch(a.$$typeof){case f:return u(t,e=e.get(null===a.key?n:a.key)||null,a,r);case m:return c(t,e=e.get(null===a.key?n:a.key)||null,a,r);case z:return b(e,t,n,a=(0,a._init)(a._payload),r)}if(D(a)||O(a))return d(t,e=e.get(n)||null,a,r,null);if("function"==typeof a.then)return b(e,t,n,ei(a),r);if(a.$$typeof===w)return b(e,t,n,Lr(t,a),r);ni(t,a)}return null}function v(o,u,c,d){if("object"==typeof c&&null!==c&&c.type===p&&null===c.key&&(c=c.props.children),"object"==typeof c&&null!==c){switch(c.$$typeof){case f:e:{for(var x=c.key;null!==u;){if(u.key===x){if((x=c.type)===p){if(7===u.tag){n(o,u.sibling),(d=l(u,c.props.children)).return=o,o=d;break e}}else if(u.elementType===x||"object"==typeof x&&null!==x&&x.$$typeof===z&&ai(x)===u.type){n(o,u.sibling),ti(d=l(u,c.props),c),d.return=o,o=d;break e}n(o,u);break}t(o,u),u=u.sibling}c.type===p?((d=qa(c.props.children,o.mode,d,c.key)).return=o,o=d):(ti(d=Ha(c.type,c.key,c.props,null,o.mode,d),c),d.return=o,o=d)}return i(o);case m:e:{for(x=c.key;null!==u;){if(u.key===x){if(4===u.tag&&u.stateNode.containerInfo===c.containerInfo&&u.stateNode.implementation===c.implementation){n(o,u.sibling),(d=l(u,c.children||[])).return=o,o=d;break e}n(o,u);break}t(o,u),u=u.sibling}(d=Qa(c,o.mode,d)).return=o,o=d}return i(o);case z:return v(o,u,c=(x=c._init)(c._payload),d)}if(D(c))return function(a,l,i,o){for(var u=null,c=null,d=l,f=l=0,m=null;null!==d&&f<i.length;f++){d.index>f?(m=d,d=null):m=d.sibling;var p=g(a,d,i[f],o);if(null===p){null===d&&(d=m);break}e&&d&&null===p.alternate&&t(a,d),l=s(p,l,f),null===c?u=p:c.sibling=p,c=p,d=m}if(f===i.length)return n(a,d),ur&&ar(a,f),u;if(null===d){for(;f<i.length;f++)null!==(d=h(a,i[f],o))&&(l=s(d,l,f),null===c?u=d:c.sibling=d,c=d);return ur&&ar(a,f),u}for(d=r(d);f<i.length;f++)null!==(m=b(d,a,f,i[f],o))&&(e&&null!==m.alternate&&d.delete(null===m.key?f:m.key),l=s(m,l,f),null===c?u=m:c.sibling=m,c=m);return e&&d.forEach((function(e){return t(a,e)})),ur&&ar(a,f),u}(o,u,c,d);if(O(c)){if("function"!=typeof(x=O(c)))throw Error(a(150));return function(l,i,o,u){if(null==o)throw Error(a(151));for(var c=null,d=null,f=i,m=i=0,p=null,v=o.next();null!==f&&!v.done;m++,v=o.next()){f.index>m?(p=f,f=null):p=f.sibling;var x=g(l,f,v.value,u);if(null===x){null===f&&(f=p);break}e&&f&&null===x.alternate&&t(l,f),i=s(x,i,m),null===d?c=x:d.sibling=x,d=x,f=p}if(v.done)return n(l,f),ur&&ar(l,m),c;if(null===f){for(;!v.done;m++,v=o.next())null!==(v=h(l,v.value,u))&&(i=s(v,i,m),null===d?c=v:d.sibling=v,d=v);return ur&&ar(l,m),c}for(f=r(f);!v.done;m++,v=o.next())null!==(v=b(f,l,m,v.value,u))&&(e&&null!==v.alternate&&f.delete(null===v.key?m:v.key),i=s(v,i,m),null===d?c=v:d.sibling=v,d=v);return e&&f.forEach((function(e){return t(l,e)})),ur&&ar(l,m),c}(o,u,c=x.call(c),d)}if("function"==typeof c.then)return v(o,u,ei(c),d);if(c.$$typeof===w)return v(o,u,Lr(o,c),d);ni(o,c)}return"string"==typeof c&&""!==c||"number"==typeof c||"bigint"==typeof c?(c=""+c,null!==u&&6===u.tag?(n(o,u.sibling),(d=l(u,c)).return=o,o=d):(n(o,u),(d=Wa(c,o.mode,d)).return=o,o=d),i(o)):n(o,u)}return function(e,t,n,a){try{Js=0;var r=v(e,t,n,a);return Xs=null,r}catch(s){if(s===Gr||s===Kr)throw s;var l=$a(29,s,null,e.mode);return l.lanes=a,l.return=e,l}}}var li=ri(!0),si=ri(!1),ii=B(null),oi=null;function ui(e){var t=e.alternate;q(mi,1&mi.current),q(ii,e),null===oi&&(null===t||null!==bl.current||null!==t.memoizedState)&&(oi=e)}function ci(e){if(22===e.tag){if(q(mi,mi.current),q(ii,e),null===oi){var t=e.alternate;null!==t&&null!==t.memoizedState&&(oi=e)}}else di()}function di(){q(mi,mi.current),q(ii,ii.current)}function fi(e){H(ii),oi===e&&(oi=null),H(mi)}var mi=B(0);function pi(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||yd(n)))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(128&t.flags)return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function hi(e,t,n,a){n=null==(n=n(a,t=e.memoizedState))?t:u({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var gi={enqueueSetState:function(e,t,n){e=e._reactInternals;var a=Au(),r=ol(a);r.payload=t,null!=n&&(r.callback=n),null!==(t=ul(e,r,a))&&($u(t,e,a),cl(t,e,a))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var a=Au(),r=ol(a);r.tag=1,r.payload=t,null!=n&&(r.callback=n),null!==(t=ul(e,r,a))&&($u(t,e,a),cl(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Au(),a=ol(n);a.tag=2,null!=t&&(a.callback=t),null!==(t=ul(e,a,n))&&($u(t,e,n),cl(t,e,n))}};function bi(e,t,n,a,r,l,s){return"function"==typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(a,l,s):!t.prototype||!t.prototype.isPureReactComponent||(!ea(n,a)||!ea(r,l))}function vi(e,t,n,a){e=t.state,"function"==typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,a),"function"==typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,a),t.state!==e&&gi.enqueueReplaceState(t,t.state,null)}function xi(e,t){var n=t;if("ref"in t)for(var a in n={},t)"ref"!==a&&(n[a]=t[a]);if(e=e.defaultProps)for(var r in n===t&&(n=u({},n)),e)void 0===n[r]&&(n[r]=e[r]);return n}var yi="function"==typeof reportError?reportError:function(e){if("object"==typeof window&&"function"==typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"==typeof e&&null!==e&&"string"==typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"==typeof process&&"function"==typeof process.emit)return void process.emit("uncaughtException",e)};function wi(e){yi(e)}function Si(e){}function ki(e){yi(e)}function Ni(e,t){try{(0,e.onUncaughtError)(t.value,{componentStack:t.stack})}catch(n){setTimeout((function(){throw n}))}}function ji(e,t,n){try{(0,e.onCaughtError)(n.value,{componentStack:n.stack,errorBoundary:1===t.tag?t.stateNode:null})}catch(a){setTimeout((function(){throw a}))}}function Ci(e,t,n){return(n=ol(n)).tag=3,n.payload={element:null},n.callback=function(){Ni(e,t)},n}function Ei(e){return(e=ol(e)).tag=3,e}function Ti(e,t,n,a){var r=n.type.getDerivedStateFromError;if("function"==typeof r){var l=a.value;e.payload=function(){return r(l)},e.callback=function(){ji(t,n,a)}}var s=n.stateNode;null!==s&&"function"==typeof s.componentDidCatch&&(e.callback=function(){ji(t,n,a),"function"!=typeof r&&(null===Tu?Tu=new Set([this]):Tu.add(this));var e=a.stack;this.componentDidCatch(a.value,{componentStack:null!==e?e:""})})}var Pi=Error(a(461)),zi=!1;function Li(e,t,n,a){t.child=null===e?si(t,null,n,a):li(t,e.child,n,a)}function _i(e,t,n,a,r){n=n.render;var l=t.ref;if("ref"in a){var s={};for(var i in a)"ref"!==i&&(s[i]=a[i])}else s=a;return Pr(t),a=Fl(e,t,n,s,l,r),i=Il(),null===e||zi?(ur&&i&&lr(t),t.flags|=1,Li(e,t,a,r),t.child):($l(e,t,r),Xi(e,t,r))}function Mi(e,t,n,a,r){if(null===e){var l=n.type;return"function"!=typeof l||Ua(l)||void 0!==l.defaultProps||null!==n.compare?((e=Ha(n.type,null,a,t,t.mode,r)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=l,Oi(e,t,l,a,r))}if(l=e.child,!Ji(e,r)){var s=l.memoizedProps;if((n=null!==(n=n.compare)?n:ea)(s,a)&&e.ref===t.ref)return Xi(e,t,r)}return t.flags|=1,(e=Va(l,a)).ref=t.ref,e.return=t,t.child=e}function Oi(e,t,n,a,r){if(null!==e){var l=e.memoizedProps;if(ea(l,a)&&e.ref===t.ref){if(zi=!1,t.pendingProps=a=l,!Ji(e,r))return t.lanes=e.lanes,Xi(e,t,r);131072&e.flags&&(zi=!0)}}return Ai(e,t,n,a,r)}function Fi(e,t,n){var a=t.pendingProps,r=a.children,l=null!==e?e.memoizedState:null;if("hidden"===a.mode){if(128&t.flags){if(a=null!==l?l.baseLanes|n:n,null!==e){for(r=t.child=e.child,l=0;null!==r;)l=l|r.lanes|r.childLanes,r=r.sibling;t.childLanes=l&~a}else t.childLanes=0,t.child=null;return Ri(e,t,a,n)}if(!(536870912&n))return t.lanes=t.childLanes=536870912,Ri(e,t,null!==l?l.baseLanes|n:n,n);t.memoizedState={baseLanes:0,cachePool:null},null!==e&&Qr(0,null!==l?l.cachePool:null),null!==l?xl(t,l):yl(),ci(t)}else null!==l?(Qr(0,l.cachePool),xl(t,l),di(),t.memoizedState=null):(null!==e&&Qr(0,null),yl(),di());return Li(e,t,r,n),t.child}function Ri(e,t,n,a){var r=Wr();return r=null===r?null:{parent:Rr._currentValue,pool:r},t.memoizedState={baseLanes:n,cachePool:r},null!==e&&Qr(0,null),yl(),ci(t),null!==e&&Er(e,t,a,!0),null}function Di(e,t){var n=t.ref;if(null===n)null!==e&&null!==e.ref&&(t.flags|=4194816);else{if("function"!=typeof n&&"object"!=typeof n)throw Error(a(284));null!==e&&e.ref===n||(t.flags|=4194816)}}function Ai(e,t,n,a,r){return Pr(t),n=Fl(e,t,n,a,void 0,r),a=Il(),null===e||zi?(ur&&a&&lr(t),t.flags|=1,Li(e,t,n,r),t.child):($l(e,t,r),Xi(e,t,r))}function Ii(e,t,n,a,r,l){return Pr(t),t.updateQueue=null,n=Dl(t,a,n,r),Rl(e),a=Il(),null===e||zi?(ur&&a&&lr(t),t.flags|=1,Li(e,t,n,l),t.child):($l(e,t,l),Xi(e,t,l))}function $i(e,t,n,a,r){if(Pr(t),null===t.stateNode){var l=Aa,s=n.contextType;"object"==typeof s&&null!==s&&(l=zr(s)),l=new n(a,l),t.memoizedState=null!==l.state&&void 0!==l.state?l.state:null,l.updater=gi,t.stateNode=l,l._reactInternals=t,(l=t.stateNode).props=a,l.state=t.memoizedState,l.refs={},sl(t),s=n.contextType,l.context="object"==typeof s&&null!==s?zr(s):Aa,l.state=t.memoizedState,"function"==typeof(s=n.getDerivedStateFromProps)&&(hi(t,n,s,a),l.state=t.memoizedState),"function"==typeof n.getDerivedStateFromProps||"function"==typeof l.getSnapshotBeforeUpdate||"function"!=typeof l.UNSAFE_componentWillMount&&"function"!=typeof l.componentWillMount||(s=l.state,"function"==typeof l.componentWillMount&&l.componentWillMount(),"function"==typeof l.UNSAFE_componentWillMount&&l.UNSAFE_componentWillMount(),s!==l.state&&gi.enqueueReplaceState(l,l.state,null),pl(t,a,l,r),ml(),l.state=t.memoizedState),"function"==typeof l.componentDidMount&&(t.flags|=4194308),a=!0}else if(null===e){l=t.stateNode;var i=t.memoizedProps,o=xi(n,i);l.props=o;var u=l.context,c=n.contextType;s=Aa,"object"==typeof c&&null!==c&&(s=zr(c));var d=n.getDerivedStateFromProps;c="function"==typeof d||"function"==typeof l.getSnapshotBeforeUpdate,i=t.pendingProps!==i,c||"function"!=typeof l.UNSAFE_componentWillReceiveProps&&"function"!=typeof l.componentWillReceiveProps||(i||u!==s)&&vi(t,l,a,s),ll=!1;var f=t.memoizedState;l.state=f,pl(t,a,l,r),ml(),u=t.memoizedState,i||f!==u||ll?("function"==typeof d&&(hi(t,n,d,a),u=t.memoizedState),(o=ll||bi(t,n,o,a,f,u,s))?(c||"function"!=typeof l.UNSAFE_componentWillMount&&"function"!=typeof l.componentWillMount||("function"==typeof l.componentWillMount&&l.componentWillMount(),"function"==typeof l.UNSAFE_componentWillMount&&l.UNSAFE_componentWillMount()),"function"==typeof l.componentDidMount&&(t.flags|=4194308)):("function"==typeof l.componentDidMount&&(t.flags|=4194308),t.memoizedProps=a,t.memoizedState=u),l.props=a,l.state=u,l.context=s,a=o):("function"==typeof l.componentDidMount&&(t.flags|=4194308),a=!1)}else{l=t.stateNode,il(e,t),c=xi(n,s=t.memoizedProps),l.props=c,d=t.pendingProps,f=l.context,u=n.contextType,o=Aa,"object"==typeof u&&null!==u&&(o=zr(u)),(u="function"==typeof(i=n.getDerivedStateFromProps)||"function"==typeof l.getSnapshotBeforeUpdate)||"function"!=typeof l.UNSAFE_componentWillReceiveProps&&"function"!=typeof l.componentWillReceiveProps||(s!==d||f!==o)&&vi(t,l,a,o),ll=!1,f=t.memoizedState,l.state=f,pl(t,a,l,r),ml();var m=t.memoizedState;s!==d||f!==m||ll||null!==e&&null!==e.dependencies&&Tr(e.dependencies)?("function"==typeof i&&(hi(t,n,i,a),m=t.memoizedState),(c=ll||bi(t,n,c,a,f,m,o)||null!==e&&null!==e.dependencies&&Tr(e.dependencies))?(u||"function"!=typeof l.UNSAFE_componentWillUpdate&&"function"!=typeof l.componentWillUpdate||("function"==typeof l.componentWillUpdate&&l.componentWillUpdate(a,m,o),"function"==typeof l.UNSAFE_componentWillUpdate&&l.UNSAFE_componentWillUpdate(a,m,o)),"function"==typeof l.componentDidUpdate&&(t.flags|=4),"function"==typeof l.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!=typeof l.componentDidUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!=typeof l.getSnapshotBeforeUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=a,t.memoizedState=m),l.props=a,l.state=m,l.context=o,a=c):("function"!=typeof l.componentDidUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!=typeof l.getSnapshotBeforeUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),a=!1)}return l=a,Di(e,t),a=!!(128&t.flags),l||a?(l=t.stateNode,n=a&&"function"!=typeof n.getDerivedStateFromError?null:l.render(),t.flags|=1,null!==e&&a?(t.child=li(t,e.child,null,r),t.child=li(t,null,n,r)):Li(e,t,n,r),t.memoizedState=l.state,e=t.child):e=Xi(e,t,r),e}function Ui(e,t,n,a){return br(),t.flags|=256,Li(e,t,n,a),t.child}var Vi={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Bi(e){return{baseLanes:e,cachePool:Zr()}}function Hi(e,t,n){return e=null!==e?e.childLanes&~n:0,t&&(e|=yu),e}function qi(e,t,n){var r,l=t.pendingProps,s=!1,i=!!(128&t.flags);if((r=i)||(r=(null===e||null!==e.memoizedState)&&!!(2&mi.current)),r&&(s=!0,t.flags&=-129),r=!!(32&t.flags),t.flags&=-33,null===e){if(ur){if(s?ui(t):di(),ur){var o,u=or;if(o=u){e:{for(o=u,u=dr;8!==o.nodeType;){if(!u){u=null;break e}if(null===(o=wd(o.nextSibling))){u=null;break e}}u=o}null!==u?(t.memoizedState={dehydrated:u,treeContext:null!==er?{id:tr,overflow:nr}:null,retryLane:536870912,hydrationErrors:null},(o=$a(18,null,null,0)).stateNode=u,o.return=t,t.child=o,ir=t,or=null,o=!0):o=!1}o||mr(t)}if(null!==(u=t.memoizedState)&&null!==(u=u.dehydrated))return yd(u)?t.lanes=32:t.lanes=536870912,null;fi(t)}return u=l.children,l=l.fallback,s?(di(),u=Qi({mode:"hidden",children:u},s=t.mode),l=qa(l,s,n,null),u.return=t,l.return=t,u.sibling=l,t.child=u,(s=t.child).memoizedState=Bi(n),s.childLanes=Hi(e,r,n),t.memoizedState=Vi,l):(ui(t),Wi(t,u))}if(null!==(o=e.memoizedState)&&null!==(u=o.dehydrated)){if(i)256&t.flags?(ui(t),t.flags&=-257,t=Zi(e,t,n)):null!==t.memoizedState?(di(),t.child=e.child,t.flags|=128,t=null):(di(),s=l.fallback,u=t.mode,l=Qi({mode:"visible",children:l.children},u),(s=qa(s,u,n,null)).flags|=2,l.return=t,s.return=t,l.sibling=s,t.child=l,li(t,e.child,null,n),(l=t.child).memoizedState=Bi(n),l.childLanes=Hi(e,r,n),t.memoizedState=Vi,t=s);else if(ui(t),yd(u)){if(r=u.nextSibling&&u.nextSibling.dataset)var c=r.dgst;r=c,(l=Error(a(419))).stack="",l.digest=r,xr({value:l,source:null,stack:null}),t=Zi(e,t,n)}else if(zi||Er(e,t,n,!1),r=0!==(n&e.childLanes),zi||r){if(null!==(r=iu)&&(0!==(l=0!==((l=42&(l=n&-n)?1:_e(l))&(r.suspendedLanes|n))?0:l)&&l!==o.retryLane))throw o.retryLane=l,Fa(e,l),$u(r,e,l),Pi;"$?"===u.data||Ku(),t=Zi(e,t,n)}else"$?"===u.data?(t.flags|=192,t.child=e.child,t=null):(e=o.treeContext,or=wd(u.nextSibling),ir=t,ur=!0,cr=null,dr=!1,null!==e&&(Xa[Ja++]=tr,Xa[Ja++]=nr,Xa[Ja++]=er,tr=e.id,nr=e.overflow,er=t),(t=Wi(t,l.children)).flags|=4096);return t}return s?(di(),s=l.fallback,u=t.mode,c=(o=e.child).sibling,(l=Va(o,{mode:"hidden",children:l.children})).subtreeFlags=65011712&o.subtreeFlags,null!==c?s=Va(c,s):(s=qa(s,u,n,null)).flags|=2,s.return=t,l.return=t,l.sibling=s,t.child=l,l=s,s=t.child,null===(u=e.child.memoizedState)?u=Bi(n):(null!==(o=u.cachePool)?(c=Rr._currentValue,o=o.parent!==c?{parent:c,pool:c}:o):o=Zr(),u={baseLanes:u.baseLanes|n,cachePool:o}),s.memoizedState=u,s.childLanes=Hi(e,r,n),t.memoizedState=Vi,l):(ui(t),e=(n=e.child).sibling,(n=Va(n,{mode:"visible",children:l.children})).return=t,n.sibling=null,null!==e&&(null===(r=t.deletions)?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=n,t.memoizedState=null,n)}function Wi(e,t){return(t=Qi({mode:"visible",children:t},e.mode)).return=e,e.child=t}function Qi(e,t){return(e=$a(22,e,null,t)).lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Zi(e,t,n){return li(t,e.child,null,n),(e=Wi(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Gi(e,t,n){e.lanes|=t;var a=e.alternate;null!==a&&(a.lanes|=t),jr(e.return,t,n)}function Yi(e,t,n,a,r){var l=e.memoizedState;null===l?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:a,tail:n,tailMode:r}:(l.isBackwards=t,l.rendering=null,l.renderingStartTime=0,l.last=a,l.tail=n,l.tailMode=r)}function Ki(e,t,n){var a=t.pendingProps,r=a.revealOrder,l=a.tail;if(Li(e,t,a.children,n),2&(a=mi.current))a=1&a|2,t.flags|=128;else{if(null!==e&&128&e.flags)e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Gi(e,n,t);else if(19===e.tag)Gi(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}a&=1}switch(q(mi,a),r){case"forwards":for(n=t.child,r=null;null!==n;)null!==(e=n.alternate)&&null===pi(e)&&(r=n),n=n.sibling;null===(n=r)?(r=t.child,t.child=null):(r=n.sibling,n.sibling=null),Yi(t,!1,r,n,l);break;case"backwards":for(n=null,r=t.child,t.child=null;null!==r;){if(null!==(e=r.alternate)&&null===pi(e)){t.child=r;break}e=r.sibling,r.sibling=n,n=r,r=e}Yi(t,!0,n,null,l);break;case"together":Yi(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Xi(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),bu|=t.lanes,0===(n&t.childLanes)){if(null===e)return null;if(Er(e,t,n,!1),0===(n&t.childLanes))return null}if(null!==e&&t.child!==e.child)throw Error(a(153));if(null!==t.child){for(n=Va(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Va(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Ji(e,t){return 0!==(e.lanes&t)||!(null===(e=e.dependencies)||!Tr(e))}function eo(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps)zi=!0;else{if(!(Ji(e,n)||128&t.flags))return zi=!1,function(e,t,n){switch(t.tag){case 3:Y(t,t.stateNode.containerInfo),kr(0,Rr,e.memoizedState.cache),br();break;case 27:case 5:X(t);break;case 4:Y(t,t.stateNode.containerInfo);break;case 10:kr(0,t.type,t.memoizedProps.value);break;case 13:var a=t.memoizedState;if(null!==a)return null!==a.dehydrated?(ui(t),t.flags|=128,null):0!==(n&t.child.childLanes)?qi(e,t,n):(ui(t),null!==(e=Xi(e,t,n))?e.sibling:null);ui(t);break;case 19:var r=!!(128&e.flags);if((a=0!==(n&t.childLanes))||(Er(e,t,n,!1),a=0!==(n&t.childLanes)),r){if(a)return Ki(e,t,n);t.flags|=128}if(null!==(r=t.memoizedState)&&(r.rendering=null,r.tail=null,r.lastEffect=null),q(mi,mi.current),a)break;return null;case 22:case 23:return t.lanes=0,Fi(e,t,n);case 24:kr(0,Rr,e.memoizedState.cache)}return Xi(e,t,n)}(e,t,n);zi=!!(131072&e.flags)}else zi=!1,ur&&1048576&t.flags&&rr(t,Ka,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var r=t.elementType,l=r._init;if(r=l(r._payload),t.type=r,"function"!=typeof r){if(null!=r){if((l=r.$$typeof)===N){t.tag=11,t=_i(null,t,r,e,n);break e}if(l===P){t.tag=14,t=Mi(null,t,r,e,n);break e}}throw t=R(r)||r,Error(a(306,t,""))}Ua(r)?(e=xi(r,e),t.tag=1,t=$i(null,t,r,e,n)):(t.tag=0,t=Ai(null,t,r,e,n))}return t;case 0:return Ai(e,t,t.type,t.pendingProps,n);case 1:return $i(e,t,r=t.type,l=xi(r,t.pendingProps),n);case 3:e:{if(Y(t,t.stateNode.containerInfo),null===e)throw Error(a(387));r=t.pendingProps;var s=t.memoizedState;l=s.element,il(e,t),pl(t,r,null,n);var i=t.memoizedState;if(r=i.cache,kr(0,Rr,r),r!==s.cache&&Cr(t,[Rr],n,!0),ml(),r=i.element,s.isDehydrated){if(s={element:r,isDehydrated:!1,cache:i.cache},t.updateQueue.baseState=s,t.memoizedState=s,256&t.flags){t=Ui(e,t,r,n);break e}if(r!==l){xr(l=Ta(Error(a(424)),t)),t=Ui(e,t,r,n);break e}if(9===(e=t.stateNode.containerInfo).nodeType)e=e.body;else e="HTML"===e.nodeName?e.ownerDocument.body:e;for(or=wd(e.firstChild),ir=t,ur=!0,cr=null,dr=!0,n=si(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(br(),r===l){t=Xi(e,t,n);break e}Li(e,t,r,n)}t=t.child}return t;case 26:return Di(e,t),null===e?(n=_d(t.type,null,t.pendingProps,null))?t.memoizedState=n:ur||(n=t.type,e=t.pendingProps,(r=id(Z.current).createElement(n))[Re]=t,r[De]=e,rd(r,n,e),Ge(r),t.stateNode=r):t.memoizedState=_d(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return X(t),null===e&&ur&&(r=t.stateNode=Nd(t.type,t.pendingProps,Z.current),ir=t,dr=!0,l=or,bd(t.type)?(Sd=l,or=wd(r.firstChild)):or=l),Li(e,t,t.pendingProps.children,n),Di(e,t),null===e&&(t.flags|=4194304),t.child;case 5:return null===e&&ur&&((l=r=or)&&(null!==(r=function(e,t,n,a){for(;1===e.nodeType;){var r=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!a&&("INPUT"!==e.nodeName||"hidden"!==e.type))break}else if(a){if(!e[Be])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if("stylesheet"===(l=e.getAttribute("rel"))&&e.hasAttribute("data-precedence"))break;if(l!==r.rel||e.getAttribute("href")!==(null==r.href||""===r.href?null:r.href)||e.getAttribute("crossorigin")!==(null==r.crossOrigin?null:r.crossOrigin)||e.getAttribute("title")!==(null==r.title?null:r.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(((l=e.getAttribute("src"))!==(null==r.src?null:r.src)||e.getAttribute("type")!==(null==r.type?null:r.type)||e.getAttribute("crossorigin")!==(null==r.crossOrigin?null:r.crossOrigin))&&l&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else{if("input"!==t||"hidden"!==e.type)return e;var l=null==r.name?null:""+r.name;if("hidden"===r.type&&e.getAttribute("name")===l)return e}if(null===(e=wd(e.nextSibling)))break}return null}(r,t.type,t.pendingProps,dr))?(t.stateNode=r,ir=t,or=wd(r.firstChild),dr=!1,l=!0):l=!1),l||mr(t)),X(t),l=t.type,s=t.pendingProps,i=null!==e?e.memoizedProps:null,r=s.children,cd(l,s)?r=null:null!==i&&cd(l,i)&&(t.flags|=32),null!==t.memoizedState&&(l=Fl(e,t,Al,null,null,n),Xd._currentValue=l),Di(e,t),Li(e,t,r,n),t.child;case 6:return null===e&&ur&&((e=n=or)&&(null!==(n=function(e,t,n){if(""===t)return null;for(;3!==e.nodeType;){if((1!==e.nodeType||"INPUT"!==e.nodeName||"hidden"!==e.type)&&!n)return null;if(null===(e=wd(e.nextSibling)))return null}return e}(n,t.pendingProps,dr))?(t.stateNode=n,ir=t,or=null,e=!0):e=!1),e||mr(t)),null;case 13:return qi(e,t,n);case 4:return Y(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=li(t,null,r,n):Li(e,t,r,n),t.child;case 11:return _i(e,t,t.type,t.pendingProps,n);case 7:return Li(e,t,t.pendingProps,n),t.child;case 8:case 12:return Li(e,t,t.pendingProps.children,n),t.child;case 10:return r=t.pendingProps,kr(0,t.type,r.value),Li(e,t,r.children,n),t.child;case 9:return l=t.type._context,r=t.pendingProps.children,Pr(t),r=r(l=zr(l)),t.flags|=1,Li(e,t,r,n),t.child;case 14:return Mi(e,t,t.type,t.pendingProps,n);case 15:return Oi(e,t,t.type,t.pendingProps,n);case 19:return Ki(e,t,n);case 31:return r=t.pendingProps,n=t.mode,r={mode:r.mode,children:r.children},null===e?((n=Qi(r,n)).ref=t.ref,t.child=n,n.return=t,t=n):((n=Va(e.child,r)).ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return Fi(e,t,n);case 24:return Pr(t),r=zr(Rr),null===e?(null===(l=Wr())&&(l=iu,s=Dr(),l.pooledCache=s,s.refCount++,null!==s&&(l.pooledCacheLanes|=n),l=s),t.memoizedState={parent:r,cache:l},sl(t),kr(0,Rr,l)):(0!==(e.lanes&n)&&(il(e,t),pl(t,null,null,n),ml()),l=e.memoizedState,s=t.memoizedState,l.parent!==r?(l={parent:r,cache:r},t.memoizedState=l,0===t.lanes&&(t.memoizedState=t.updateQueue.baseState=l),kr(0,Rr,r)):(r=s.cache,kr(0,Rr,r),r!==l.cache&&Cr(t,[Rr],n,!0))),Li(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(a(156,t.tag))}function to(e){e.flags|=4}function no(e,t){if("stylesheet"!==t.type||4&t.state.loading)e.flags&=-16777217;else if(e.flags|=16777216,!qd(t)){if(null!==(t=ii.current)&&((4194048&uu)===uu?null!==oi:(62914560&uu)!==uu&&!(536870912&uu)||t!==oi))throw nl=Xr,Yr;e.flags|=8192}}function ao(e,t){null!==t&&(e.flags|=4),16384&e.flags&&(t=22!==e.tag?Ee():536870912,e.lanes|=t,wu|=t)}function ro(e,t){if(!ur)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var a=null;null!==n;)null!==n.alternate&&(a=n),n=n.sibling;null===a?t||null===e.tail?e.tail=null:e.tail.sibling=null:a.sibling=null}}function lo(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,a=0;if(t)for(var r=e.child;null!==r;)n|=r.lanes|r.childLanes,a|=65011712&r.subtreeFlags,a|=65011712&r.flags,r.return=e,r=r.sibling;else for(r=e.child;null!==r;)n|=r.lanes|r.childLanes,a|=r.subtreeFlags,a|=r.flags,r.return=e,r=r.sibling;return e.subtreeFlags|=a,e.childLanes=n,t}function so(e,t,n){var r=t.pendingProps;switch(sr(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:case 1:return lo(t),null;case 3:return n=t.stateNode,r=null,null!==e&&(r=e.memoizedState.cache),t.memoizedState.cache!==r&&(t.flags|=2048),Nr(Rr),K(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),null!==e&&null!==e.child||(gr(t)?to(t):null===e||e.memoizedState.isDehydrated&&!(256&t.flags)||(t.flags|=1024,vr())),lo(t),null;case 26:return n=t.memoizedState,null===e?(to(t),null!==n?(lo(t),no(t,n)):(lo(t),t.flags&=-16777217)):n?n!==e.memoizedState?(to(t),lo(t),no(t,n)):(lo(t),t.flags&=-16777217):(e.memoizedProps!==r&&to(t),lo(t),t.flags&=-16777217),null;case 27:J(t),n=Z.current;var l=t.type;if(null!==e&&null!=t.stateNode)e.memoizedProps!==r&&to(t);else{if(!r){if(null===t.stateNode)throw Error(a(166));return lo(t),null}e=W.current,gr(t)?pr(t):(e=Nd(l,r,n),t.stateNode=e,to(t))}return lo(t),null;case 5:if(J(t),n=t.type,null!==e&&null!=t.stateNode)e.memoizedProps!==r&&to(t);else{if(!r){if(null===t.stateNode)throw Error(a(166));return lo(t),null}if(e=W.current,gr(t))pr(t);else{switch(l=id(Z.current),e){case 1:e=l.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=l.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=l.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=l.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":(e=l.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e="string"==typeof r.is?l.createElement("select",{is:r.is}):l.createElement("select"),r.multiple?e.multiple=!0:r.size&&(e.size=r.size);break;default:e="string"==typeof r.is?l.createElement(n,{is:r.is}):l.createElement(n)}}e[Re]=t,e[De]=r;e:for(l=t.child;null!==l;){if(5===l.tag||6===l.tag)e.appendChild(l.stateNode);else if(4!==l.tag&&27!==l.tag&&null!==l.child){l.child.return=l,l=l.child;continue}if(l===t)break e;for(;null===l.sibling;){if(null===l.return||l.return===t)break e;l=l.return}l.sibling.return=l.return,l=l.sibling}t.stateNode=e;e:switch(rd(e,n,r),n){case"button":case"input":case"select":case"textarea":e=!!r.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&to(t)}}return lo(t),t.flags&=-16777217,null;case 6:if(e&&null!=t.stateNode)e.memoizedProps!==r&&to(t);else{if("string"!=typeof r&&null===t.stateNode)throw Error(a(166));if(e=Z.current,gr(t)){if(e=t.stateNode,n=t.memoizedProps,r=null,null!==(l=ir))switch(l.tag){case 27:case 5:r=l.memoizedProps}e[Re]=t,(e=!!(e.nodeValue===n||null!==r&&!0===r.suppressHydrationWarning||ed(e.nodeValue,n)))||mr(t)}else(e=id(e).createTextNode(r))[Re]=t,t.stateNode=e}return lo(t),null;case 13:if(r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(l=gr(t),null!==r&&null!==r.dehydrated){if(null===e){if(!l)throw Error(a(318));if(!(l=null!==(l=t.memoizedState)?l.dehydrated:null))throw Error(a(317));l[Re]=t}else br(),!(128&t.flags)&&(t.memoizedState=null),t.flags|=4;lo(t),l=!1}else l=vr(),null!==e&&null!==e.memoizedState&&(e.memoizedState.hydrationErrors=l),l=!0;if(!l)return 256&t.flags?(fi(t),t):(fi(t),null)}if(fi(t),128&t.flags)return t.lanes=n,t;if(n=null!==r,e=null!==e&&null!==e.memoizedState,n){l=null,null!==(r=t.child).alternate&&null!==r.alternate.memoizedState&&null!==r.alternate.memoizedState.cachePool&&(l=r.alternate.memoizedState.cachePool.pool);var s=null;null!==r.memoizedState&&null!==r.memoizedState.cachePool&&(s=r.memoizedState.cachePool.pool),s!==l&&(r.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),ao(t,t.updateQueue),lo(t),null;case 4:return K(),null===e&&Hc(t.stateNode.containerInfo),lo(t),null;case 10:return Nr(t.type),lo(t),null;case 19:if(H(mi),null===(l=t.memoizedState))return lo(t),null;if(r=!!(128&t.flags),null===(s=l.rendering))if(r)ro(l,!1);else{if(0!==gu||null!==e&&128&e.flags)for(e=t.child;null!==e;){if(null!==(s=pi(e))){for(t.flags|=128,ro(l,!1),e=s.updateQueue,t.updateQueue=e,ao(t,e),t.subtreeFlags=0,e=n,n=t.child;null!==n;)Ba(n,e),n=n.sibling;return q(mi,1&mi.current|2),t.child}e=e.sibling}null!==l.tail&&le()>Cu&&(t.flags|=128,r=!0,ro(l,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=pi(s))){if(t.flags|=128,r=!0,e=e.updateQueue,t.updateQueue=e,ao(t,e),ro(l,!0),null===l.tail&&"hidden"===l.tailMode&&!s.alternate&&!ur)return lo(t),null}else 2*le()-l.renderingStartTime>Cu&&536870912!==n&&(t.flags|=128,r=!0,ro(l,!1),t.lanes=4194304);l.isBackwards?(s.sibling=t.child,t.child=s):(null!==(e=l.last)?e.sibling=s:t.child=s,l.last=s)}return null!==l.tail?(t=l.tail,l.rendering=t,l.tail=t.sibling,l.renderingStartTime=le(),t.sibling=null,e=mi.current,q(mi,r?1&e|2:1&e),t):(lo(t),null);case 22:case 23:return fi(t),wl(),r=null!==t.memoizedState,null!==e?null!==e.memoizedState!==r&&(t.flags|=8192):r&&(t.flags|=8192),r?!!(536870912&n)&&!(128&t.flags)&&(lo(t),6&t.subtreeFlags&&(t.flags|=8192)):lo(t),null!==(n=t.updateQueue)&&ao(t,n.retryQueue),n=null,null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),r=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(r=t.memoizedState.cachePool.pool),r!==n&&(t.flags|=2048),null!==e&&H(qr),null;case 24:return n=null,null!==e&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),Nr(Rr),lo(t),null;case 25:case 30:return null}throw Error(a(156,t.tag))}function io(e,t){switch(sr(t),t.tag){case 1:return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Nr(Rr),K(),65536&(e=t.flags)&&!(128&e)?(t.flags=-65537&e|128,t):null;case 26:case 27:case 5:return J(t),null;case 13:if(fi(t),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(a(340));br()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return H(mi),null;case 4:return K(),null;case 10:return Nr(t.type),null;case 22:case 23:return fi(t),wl(),null!==e&&H(qr),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 24:return Nr(Rr),null;default:return null}}function oo(e,t){switch(sr(t),t.tag){case 3:Nr(Rr),K();break;case 26:case 27:case 5:J(t);break;case 4:K();break;case 13:fi(t);break;case 19:H(mi);break;case 10:Nr(t.type);break;case 22:case 23:fi(t),wl(),null!==e&&H(qr);break;case 24:Nr(Rr)}}function uo(e,t){try{var n=t.updateQueue,a=null!==n?n.lastEffect:null;if(null!==a){var r=a.next;n=r;do{if((n.tag&e)===e){a=void 0;var l=n.create,s=n.inst;a=l(),s.destroy=a}n=n.next}while(n!==r)}}catch(i){pc(t,t.return,i)}}function co(e,t,n){try{var a=t.updateQueue,r=null!==a?a.lastEffect:null;if(null!==r){var l=r.next;a=l;do{if((a.tag&e)===e){var s=a.inst,i=s.destroy;if(void 0!==i){s.destroy=void 0,r=t;var o=n,u=i;try{u()}catch(c){pc(r,o,c)}}}a=a.next}while(a!==l)}}catch(c){pc(t,t.return,c)}}function fo(e){var t=e.updateQueue;if(null!==t){var n=e.stateNode;try{gl(t,n)}catch(a){pc(e,e.return,a)}}}function mo(e,t,n){n.props=xi(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(a){pc(e,t,a)}}function po(e,t){try{var n=e.ref;if(null!==n){switch(e.tag){case 26:case 27:case 5:var a=e.stateNode;break;default:a=e.stateNode}"function"==typeof n?e.refCleanup=n(a):n.current=a}}catch(r){pc(e,t,r)}}function ho(e,t){var n=e.ref,a=e.refCleanup;if(null!==n)if("function"==typeof a)try{a()}catch(r){pc(e,t,r)}finally{e.refCleanup=null,null!=(e=e.alternate)&&(e.refCleanup=null)}else if("function"==typeof n)try{n(null)}catch(l){pc(e,t,l)}else n.current=null}function go(e){var t=e.type,n=e.memoizedProps,a=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&a.focus();break e;case"img":n.src?a.src=n.src:n.srcSet&&(a.srcset=n.srcSet)}}catch(r){pc(e,e.return,r)}}function bo(e,t,n){try{var r=e.stateNode;!function(e,t,n,r){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var l=null,s=null,i=null,o=null,u=null,c=null,d=null;for(p in n){var f=n[p];if(n.hasOwnProperty(p)&&null!=f)switch(p){case"checked":case"value":break;case"defaultValue":u=f;default:r.hasOwnProperty(p)||nd(e,t,p,null,r,f)}}for(var m in r){var p=r[m];if(f=n[m],r.hasOwnProperty(m)&&(null!=p||null!=f))switch(m){case"type":s=p;break;case"name":l=p;break;case"checked":c=p;break;case"defaultChecked":d=p;break;case"value":i=p;break;case"defaultValue":o=p;break;case"children":case"dangerouslySetInnerHTML":if(null!=p)throw Error(a(137,t));break;default:p!==f&&nd(e,t,m,p,r,f)}}return void yt(e,i,o,u,c,d,s,l);case"select":for(s in p=i=o=m=null,n)if(u=n[s],n.hasOwnProperty(s)&&null!=u)switch(s){case"value":break;case"multiple":p=u;default:r.hasOwnProperty(s)||nd(e,t,s,null,r,u)}for(l in r)if(s=r[l],u=n[l],r.hasOwnProperty(l)&&(null!=s||null!=u))switch(l){case"value":m=s;break;case"defaultValue":o=s;break;case"multiple":i=s;default:s!==u&&nd(e,t,l,s,r,u)}return t=o,n=i,r=p,void(null!=m?kt(e,!!n,m,!1):!!r!=!!n&&(null!=t?kt(e,!!n,t,!0):kt(e,!!n,n?[]:"",!1)));case"textarea":for(o in p=m=null,n)if(l=n[o],n.hasOwnProperty(o)&&null!=l&&!r.hasOwnProperty(o))switch(o){case"value":case"children":break;default:nd(e,t,o,null,r,l)}for(i in r)if(l=r[i],s=n[i],r.hasOwnProperty(i)&&(null!=l||null!=s))switch(i){case"value":m=l;break;case"defaultValue":p=l;break;case"children":break;case"dangerouslySetInnerHTML":if(null!=l)throw Error(a(91));break;default:l!==s&&nd(e,t,i,l,r,s)}return void Nt(e,m,p);case"option":for(var h in n)if(m=n[h],n.hasOwnProperty(h)&&null!=m&&!r.hasOwnProperty(h))if("selected"===h)e.selected=!1;else nd(e,t,h,null,r,m);for(u in r)if(m=r[u],p=n[u],r.hasOwnProperty(u)&&m!==p&&(null!=m||null!=p))if("selected"===u)e.selected=m&&"function"!=typeof m&&"symbol"!=typeof m;else nd(e,t,u,m,r,p);return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var g in n)m=n[g],n.hasOwnProperty(g)&&null!=m&&!r.hasOwnProperty(g)&&nd(e,t,g,null,r,m);for(c in r)if(m=r[c],p=n[c],r.hasOwnProperty(c)&&m!==p&&(null!=m||null!=p))switch(c){case"children":case"dangerouslySetInnerHTML":if(null!=m)throw Error(a(137,t));break;default:nd(e,t,c,m,r,p)}return;default:if(zt(t)){for(var b in n)m=n[b],n.hasOwnProperty(b)&&void 0!==m&&!r.hasOwnProperty(b)&&ad(e,t,b,void 0,r,m);for(d in r)m=r[d],p=n[d],!r.hasOwnProperty(d)||m===p||void 0===m&&void 0===p||ad(e,t,d,m,r,p);return}}for(var v in n)m=n[v],n.hasOwnProperty(v)&&null!=m&&!r.hasOwnProperty(v)&&nd(e,t,v,null,r,m);for(f in r)m=r[f],p=n[f],!r.hasOwnProperty(f)||m===p||null==m&&null==p||nd(e,t,f,m,r,p)}(r,e.type,n,t),r[De]=t}catch(l){pc(e,e.return,l)}}function vo(e){return 5===e.tag||3===e.tag||26===e.tag||27===e.tag&&bd(e.type)||4===e.tag}function xo(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||vo(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(27===e.tag&&bd(e.type))continue e;if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function yo(e,t,n){var a=e.tag;if(5===a||6===a)e=e.stateNode,t?(9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).insertBefore(e,t):((t=9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).appendChild(e),null!=(n=n._reactRootContainer)||null!==t.onclick||(t.onclick=td));else if(4!==a&&(27===a&&bd(e.type)&&(n=e.stateNode,t=null),null!==(e=e.child)))for(yo(e,t,n),e=e.sibling;null!==e;)yo(e,t,n),e=e.sibling}function wo(e,t,n){var a=e.tag;if(5===a||6===a)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==a&&(27===a&&bd(e.type)&&(n=e.stateNode),null!==(e=e.child)))for(wo(e,t,n),e=e.sibling;null!==e;)wo(e,t,n),e=e.sibling}function So(e){var t=e.stateNode,n=e.memoizedProps;try{for(var a=e.type,r=t.attributes;r.length;)t.removeAttributeNode(r[0]);rd(t,a,n),t[Re]=e,t[De]=n}catch(l){pc(e,e.return,l)}}var ko=!1,No=!1,jo=!1,Co="function"==typeof WeakSet?WeakSet:Set,Eo=null;function To(e,t,n){var a=n.flags;switch(n.tag){case 0:case 11:case 15:Uo(e,n),4&a&&uo(5,n);break;case 1:if(Uo(e,n),4&a)if(e=n.stateNode,null===t)try{e.componentDidMount()}catch(s){pc(n,n.return,s)}else{var r=xi(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(r,t,e.__reactInternalSnapshotBeforeUpdate)}catch(i){pc(n,n.return,i)}}64&a&&fo(n),512&a&&po(n,n.return);break;case 3:if(Uo(e,n),64&a&&null!==(e=n.updateQueue)){if(t=null,null!==n.child)switch(n.child.tag){case 27:case 5:case 1:t=n.child.stateNode}try{gl(e,t)}catch(s){pc(n,n.return,s)}}break;case 27:null===t&&4&a&&So(n);case 26:case 5:Uo(e,n),null===t&&4&a&&go(n),512&a&&po(n,n.return);break;case 12:Uo(e,n);break;case 13:Uo(e,n),4&a&&Oo(e,n),64&a&&(null!==(e=n.memoizedState)&&(null!==(e=e.dehydrated)&&function(e,t){var n=e.ownerDocument;if("$?"!==e.data||"complete"===n.readyState)t();else{var a=function(){t(),n.removeEventListener("DOMContentLoaded",a)};n.addEventListener("DOMContentLoaded",a),e._reactRetry=a}}(e,n=vc.bind(null,n))));break;case 22:if(!(a=null!==n.memoizedState||ko)){t=null!==t&&null!==t.memoizedState||No,r=ko;var l=No;ko=a,(No=t)&&!l?Bo(e,n,!!(8772&n.subtreeFlags)):Uo(e,n),ko=r,No=l}break;case 30:break;default:Uo(e,n)}}function Po(e){var t=e.alternate;null!==t&&(e.alternate=null,Po(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&He(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var zo=null,Lo=!1;function _o(e,t,n){for(n=n.child;null!==n;)Mo(e,t,n),n=n.sibling}function Mo(e,t,n){if(he&&"function"==typeof he.onCommitFiberUnmount)try{he.onCommitFiberUnmount(pe,n)}catch(l){}switch(n.tag){case 26:No||ho(n,t),_o(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode).parentNode.removeChild(n);break;case 27:No||ho(n,t);var a=zo,r=Lo;bd(n.type)&&(zo=n.stateNode,Lo=!1),_o(e,t,n),jd(n.stateNode),zo=a,Lo=r;break;case 5:No||ho(n,t);case 6:if(a=zo,r=Lo,zo=null,_o(e,t,n),Lo=r,null!==(zo=a))if(Lo)try{(9===zo.nodeType?zo.body:"HTML"===zo.nodeName?zo.ownerDocument.body:zo).removeChild(n.stateNode)}catch(s){pc(n,t,s)}else try{zo.removeChild(n.stateNode)}catch(s){pc(n,t,s)}break;case 18:null!==zo&&(Lo?(vd(9===(e=zo).nodeType?e.body:"HTML"===e.nodeName?e.ownerDocument.body:e,n.stateNode),_f(e)):vd(zo,n.stateNode));break;case 4:a=zo,r=Lo,zo=n.stateNode.containerInfo,Lo=!0,_o(e,t,n),zo=a,Lo=r;break;case 0:case 11:case 14:case 15:No||co(2,n,t),No||co(4,n,t),_o(e,t,n);break;case 1:No||(ho(n,t),"function"==typeof(a=n.stateNode).componentWillUnmount&&mo(n,t,a)),_o(e,t,n);break;case 21:_o(e,t,n);break;case 22:No=(a=No)||null!==n.memoizedState,_o(e,t,n),No=a;break;default:_o(e,t,n)}}function Oo(e,t){if(null===t.memoizedState&&(null!==(e=t.alternate)&&(null!==(e=e.memoizedState)&&null!==(e=e.dehydrated))))try{_f(e)}catch(n){pc(t,t.return,n)}}function Fo(e,t){var n=function(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return null===t&&(t=e.stateNode=new Co),t;case 22:return null===(t=(e=e.stateNode)._retryCache)&&(t=e._retryCache=new Co),t;default:throw Error(a(435,e.tag))}}(e);t.forEach((function(t){var a=xc.bind(null,e,t);n.has(t)||(n.add(t),t.then(a,a))}))}function Ro(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var l=n[r],s=e,i=t,o=i;e:for(;null!==o;){switch(o.tag){case 27:if(bd(o.type)){zo=o.stateNode,Lo=!1;break e}break;case 5:zo=o.stateNode,Lo=!1;break e;case 3:case 4:zo=o.stateNode.containerInfo,Lo=!0;break e}o=o.return}if(null===zo)throw Error(a(160));Mo(s,i,l),zo=null,Lo=!1,null!==(s=l.alternate)&&(s.return=null),l.return=null}if(13878&t.subtreeFlags)for(t=t.child;null!==t;)Ao(t,e),t=t.sibling}var Do=null;function Ao(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Ro(t,e),Io(e),4&r&&(co(3,e,e.return),uo(3,e),co(5,e,e.return));break;case 1:Ro(t,e),Io(e),512&r&&(No||null===n||ho(n,n.return)),64&r&&ko&&(null!==(e=e.updateQueue)&&(null!==(r=e.callbacks)&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=null===n?r:n.concat(r))));break;case 26:var l=Do;if(Ro(t,e),Io(e),512&r&&(No||null===n||ho(n,n.return)),4&r){var s=null!==n?n.memoizedState:null;if(r=e.memoizedState,null===n)if(null===r)if(null===e.stateNode){e:{r=e.type,n=e.memoizedProps,l=l.ownerDocument||l;t:switch(r){case"title":(!(s=l.getElementsByTagName("title")[0])||s[Be]||s[Re]||"http://www.w3.org/2000/svg"===s.namespaceURI||s.hasAttribute("itemprop"))&&(s=l.createElement(r),l.head.insertBefore(s,l.querySelector("head > title"))),rd(s,r,n),s[Re]=e,Ge(s),r=s;break e;case"link":var i=Bd("link","href",l).get(r+(n.href||""));if(i)for(var o=0;o<i.length;o++)if((s=i[o]).getAttribute("href")===(null==n.href||""===n.href?null:n.href)&&s.getAttribute("rel")===(null==n.rel?null:n.rel)&&s.getAttribute("title")===(null==n.title?null:n.title)&&s.getAttribute("crossorigin")===(null==n.crossOrigin?null:n.crossOrigin)){i.splice(o,1);break t}rd(s=l.createElement(r),r,n),l.head.appendChild(s);break;case"meta":if(i=Bd("meta","content",l).get(r+(n.content||"")))for(o=0;o<i.length;o++)if((s=i[o]).getAttribute("content")===(null==n.content?null:""+n.content)&&s.getAttribute("name")===(null==n.name?null:n.name)&&s.getAttribute("property")===(null==n.property?null:n.property)&&s.getAttribute("http-equiv")===(null==n.httpEquiv?null:n.httpEquiv)&&s.getAttribute("charset")===(null==n.charSet?null:n.charSet)){i.splice(o,1);break t}rd(s=l.createElement(r),r,n),l.head.appendChild(s);break;default:throw Error(a(468,r))}s[Re]=e,Ge(s),r=s}e.stateNode=r}else Hd(l,e.type,e.stateNode);else e.stateNode=Ad(l,r,e.memoizedProps);else s!==r?(null===s?null!==n.stateNode&&(n=n.stateNode).parentNode.removeChild(n):s.count--,null===r?Hd(l,e.type,e.stateNode):Ad(l,r,e.memoizedProps)):null===r&&null!==e.stateNode&&bo(e,e.memoizedProps,n.memoizedProps)}break;case 27:Ro(t,e),Io(e),512&r&&(No||null===n||ho(n,n.return)),null!==n&&4&r&&bo(e,e.memoizedProps,n.memoizedProps);break;case 5:if(Ro(t,e),Io(e),512&r&&(No||null===n||ho(n,n.return)),32&e.flags){l=e.stateNode;try{Ct(l,"")}catch(p){pc(e,e.return,p)}}4&r&&null!=e.stateNode&&bo(e,l=e.memoizedProps,null!==n?n.memoizedProps:l),1024&r&&(jo=!0);break;case 6:if(Ro(t,e),Io(e),4&r){if(null===e.stateNode)throw Error(a(162));r=e.memoizedProps,n=e.stateNode;try{n.nodeValue=r}catch(p){pc(e,e.return,p)}}break;case 3:if(Vd=null,l=Do,Do=Td(t.containerInfo),Ro(t,e),Do=l,Io(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{_f(t.containerInfo)}catch(p){pc(e,e.return,p)}jo&&(jo=!1,$o(e));break;case 4:r=Do,Do=Td(e.stateNode.containerInfo),Ro(t,e),Io(e),Do=r;break;case 12:default:Ro(t,e),Io(e);break;case 13:Ro(t,e),Io(e),8192&e.child.flags&&null!==e.memoizedState!=(null!==n&&null!==n.memoizedState)&&(ju=le()),4&r&&(null!==(r=e.updateQueue)&&(e.updateQueue=null,Fo(e,r)));break;case 22:l=null!==e.memoizedState;var u=null!==n&&null!==n.memoizedState,c=ko,d=No;if(ko=c||l,No=d||u,Ro(t,e),No=d,ko=c,Io(e),8192&r)e:for(t=e.stateNode,t._visibility=l?-2&t._visibility:1|t._visibility,l&&(null===n||u||ko||No||Vo(e)),n=null,t=e;;){if(5===t.tag||26===t.tag){if(null===n){u=n=t;try{if(s=u.stateNode,l)"function"==typeof(i=s.style).setProperty?i.setProperty("display","none","important"):i.display="none";else{o=u.stateNode;var f=u.memoizedProps.style,m=null!=f&&f.hasOwnProperty("display")?f.display:null;o.style.display=null==m||"boolean"==typeof m?"":(""+m).trim()}}catch(p){pc(u,u.return,p)}}}else if(6===t.tag){if(null===n){u=t;try{u.stateNode.nodeValue=l?"":u.memoizedProps}catch(p){pc(u,u.return,p)}}}else if((22!==t.tag&&23!==t.tag||null===t.memoizedState||t===e)&&null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;null===t.sibling;){if(null===t.return||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}4&r&&(null!==(r=e.updateQueue)&&(null!==(n=r.retryQueue)&&(r.retryQueue=null,Fo(e,n))));break;case 19:Ro(t,e),Io(e),4&r&&(null!==(r=e.updateQueue)&&(e.updateQueue=null,Fo(e,r)));case 30:case 21:}}function Io(e){var t=e.flags;if(2&t){try{for(var n,r=e.return;null!==r;){if(vo(r)){n=r;break}r=r.return}if(null==n)throw Error(a(160));switch(n.tag){case 27:var l=n.stateNode;wo(e,xo(e),l);break;case 5:var s=n.stateNode;32&n.flags&&(Ct(s,""),n.flags&=-33),wo(e,xo(e),s);break;case 3:case 4:var i=n.stateNode.containerInfo;yo(e,xo(e),i);break;default:throw Error(a(161))}}catch(o){pc(e,e.return,o)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function $o(e){if(1024&e.subtreeFlags)for(e=e.child;null!==e;){var t=e;$o(t),5===t.tag&&1024&t.flags&&t.stateNode.reset(),e=e.sibling}}function Uo(e,t){if(8772&t.subtreeFlags)for(t=t.child;null!==t;)To(e,t.alternate,t),t=t.sibling}function Vo(e){for(e=e.child;null!==e;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:co(4,t,t.return),Vo(t);break;case 1:ho(t,t.return);var n=t.stateNode;"function"==typeof n.componentWillUnmount&&mo(t,t.return,n),Vo(t);break;case 27:jd(t.stateNode);case 26:case 5:ho(t,t.return),Vo(t);break;case 22:null===t.memoizedState&&Vo(t);break;default:Vo(t)}e=e.sibling}}function Bo(e,t,n){for(n=n&&!!(8772&t.subtreeFlags),t=t.child;null!==t;){var a=t.alternate,r=e,l=t,s=l.flags;switch(l.tag){case 0:case 11:case 15:Bo(r,l,n),uo(4,l);break;case 1:if(Bo(r,l,n),"function"==typeof(r=(a=l).stateNode).componentDidMount)try{r.componentDidMount()}catch(u){pc(a,a.return,u)}if(null!==(r=(a=l).updateQueue)){var i=a.stateNode;try{var o=r.shared.hiddenCallbacks;if(null!==o)for(r.shared.hiddenCallbacks=null,r=0;r<o.length;r++)hl(o[r],i)}catch(u){pc(a,a.return,u)}}n&&64&s&&fo(l),po(l,l.return);break;case 27:So(l);case 26:case 5:Bo(r,l,n),n&&null===a&&4&s&&go(l),po(l,l.return);break;case 12:Bo(r,l,n);break;case 13:Bo(r,l,n),n&&4&s&&Oo(r,l);break;case 22:null===l.memoizedState&&Bo(r,l,n),po(l,l.return);break;case 30:break;default:Bo(r,l,n)}t=t.sibling}}function Ho(e,t){var n=null;null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),e=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(e=t.memoizedState.cachePool.pool),e!==n&&(null!=e&&e.refCount++,null!=n&&Ar(n))}function qo(e,t){e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&Ar(e))}function Wo(e,t,n,a){if(10256&t.subtreeFlags)for(t=t.child;null!==t;)Qo(e,t,n,a),t=t.sibling}function Qo(e,t,n,a){var r=t.flags;switch(t.tag){case 0:case 11:case 15:Wo(e,t,n,a),2048&r&&uo(9,t);break;case 1:case 13:default:Wo(e,t,n,a);break;case 3:Wo(e,t,n,a),2048&r&&(e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&Ar(e)));break;case 12:if(2048&r){Wo(e,t,n,a),e=t.stateNode;try{var l=t.memoizedProps,s=l.id,i=l.onPostCommit;"function"==typeof i&&i(s,null===t.alternate?"mount":"update",e.passiveEffectDuration,-0)}catch(o){pc(t,t.return,o)}}else Wo(e,t,n,a);break;case 23:break;case 22:l=t.stateNode,s=t.alternate,null!==t.memoizedState?2&l._visibility?Wo(e,t,n,a):Go(e,t):2&l._visibility?Wo(e,t,n,a):(l._visibility|=2,Zo(e,t,n,a,!!(10256&t.subtreeFlags))),2048&r&&Ho(s,t);break;case 24:Wo(e,t,n,a),2048&r&&qo(t.alternate,t)}}function Zo(e,t,n,a,r){for(r=r&&!!(10256&t.subtreeFlags),t=t.child;null!==t;){var l=e,s=t,i=n,o=a,u=s.flags;switch(s.tag){case 0:case 11:case 15:Zo(l,s,i,o,r),uo(8,s);break;case 23:break;case 22:var c=s.stateNode;null!==s.memoizedState?2&c._visibility?Zo(l,s,i,o,r):Go(l,s):(c._visibility|=2,Zo(l,s,i,o,r)),r&&2048&u&&Ho(s.alternate,s);break;case 24:Zo(l,s,i,o,r),r&&2048&u&&qo(s.alternate,s);break;default:Zo(l,s,i,o,r)}t=t.sibling}}function Go(e,t){if(10256&t.subtreeFlags)for(t=t.child;null!==t;){var n=e,a=t,r=a.flags;switch(a.tag){case 22:Go(n,a),2048&r&&Ho(a.alternate,a);break;case 24:Go(n,a),2048&r&&qo(a.alternate,a);break;default:Go(n,a)}t=t.sibling}}var Yo=8192;function Ko(e){if(e.subtreeFlags&Yo)for(e=e.child;null!==e;)Xo(e),e=e.sibling}function Xo(e){switch(e.tag){case 26:Ko(e),e.flags&Yo&&null!==e.memoizedState&&function(e,t,n){if(null===Wd)throw Error(a(475));var r=Wd;if(!("stylesheet"!==t.type||"string"==typeof n.media&&!1===matchMedia(n.media).matches||4&t.state.loading)){if(null===t.instance){var l=Md(n.href),s=e.querySelector(Od(l));if(s)return null!==(e=s._p)&&"object"==typeof e&&"function"==typeof e.then&&(r.count++,r=Zd.bind(r),e.then(r,r)),t.state.loading|=4,t.instance=s,void Ge(s);s=e.ownerDocument||e,n=Fd(n),(l=Cd.get(l))&&$d(n,l),Ge(s=s.createElement("link"));var i=s;i._p=new Promise((function(e,t){i.onload=e,i.onerror=t})),rd(s,"link",n),t.instance=s}null===r.stylesheets&&(r.stylesheets=new Map),r.stylesheets.set(t,e),(e=t.state.preload)&&!(3&t.state.loading)&&(r.count++,t=Zd.bind(r),e.addEventListener("load",t),e.addEventListener("error",t))}}(Do,e.memoizedState,e.memoizedProps);break;case 5:default:Ko(e);break;case 3:case 4:var t=Do;Do=Td(e.stateNode.containerInfo),Ko(e),Do=t;break;case 22:null===e.memoizedState&&(null!==(t=e.alternate)&&null!==t.memoizedState?(t=Yo,Yo=16777216,Ko(e),Yo=t):Ko(e))}}function Jo(e){var t=e.alternate;if(null!==t&&null!==(e=t.child)){t.child=null;do{t=e.sibling,e.sibling=null,e=t}while(null!==e)}}function eu(e){var t=e.deletions;if(16&e.flags){if(null!==t)for(var n=0;n<t.length;n++){var a=t[n];Eo=a,au(a,e)}Jo(e)}if(10256&e.subtreeFlags)for(e=e.child;null!==e;)tu(e),e=e.sibling}function tu(e){switch(e.tag){case 0:case 11:case 15:eu(e),2048&e.flags&&co(9,e,e.return);break;case 3:case 12:default:eu(e);break;case 22:var t=e.stateNode;null!==e.memoizedState&&2&t._visibility&&(null===e.return||13!==e.return.tag)?(t._visibility&=-3,nu(e)):eu(e)}}function nu(e){var t=e.deletions;if(16&e.flags){if(null!==t)for(var n=0;n<t.length;n++){var a=t[n];Eo=a,au(a,e)}Jo(e)}for(e=e.child;null!==e;){switch((t=e).tag){case 0:case 11:case 15:co(8,t,t.return),nu(t);break;case 22:2&(n=t.stateNode)._visibility&&(n._visibility&=-3,nu(t));break;default:nu(t)}e=e.sibling}}function au(e,t){for(;null!==Eo;){var n=Eo;switch(n.tag){case 0:case 11:case 15:co(8,n,t);break;case 23:case 22:if(null!==n.memoizedState&&null!==n.memoizedState.cachePool){var a=n.memoizedState.cachePool.pool;null!=a&&a.refCount++}break;case 24:Ar(n.memoizedState.cache)}if(null!==(a=n.child))a.return=n,Eo=a;else e:for(n=e;null!==Eo;){var r=(a=Eo).sibling,l=a.return;if(Po(a),a===n){Eo=null;break e}if(null!==r){r.return=l,Eo=r;break e}Eo=l}}}var ru={getCacheForType:function(e){var t=zr(Rr),n=t.data.get(e);return void 0===n&&(n=e(),t.data.set(e,n)),n}},lu="function"==typeof WeakMap?WeakMap:Map,su=0,iu=null,ou=null,uu=0,cu=0,du=null,fu=!1,mu=!1,pu=!1,hu=0,gu=0,bu=0,vu=0,xu=0,yu=0,wu=0,Su=null,ku=null,Nu=!1,ju=0,Cu=1/0,Eu=null,Tu=null,Pu=0,zu=null,Lu=null,_u=0,Mu=0,Ou=null,Fu=null,Ru=0,Du=null;function Au(){if(2&su&&0!==uu)return uu&-uu;if(null!==A.T){return 0!==Ur?Ur:Mc()}return Oe()}function Iu(){0===yu&&(yu=536870912&uu&&!ur?536870912:Ce());var e=ii.current;return null!==e&&(e.flags|=32),yu}function $u(e,t,n){(e!==iu||2!==cu&&9!==cu)&&null===e.cancelPendingCommit||(Qu(e,0),Hu(e,uu,yu,!1)),Pe(e,n),2&su&&e===iu||(e===iu&&(!(2&su)&&(vu|=n),4===gu&&Hu(e,uu,yu,!1)),Cc(e))}function Uu(e,t,n){if(6&su)throw Error(a(327));for(var r=!n&&!(124&t)&&0===(t&e.expiredLanes)||Ne(e,t),l=r?function(e,t){var n=su;su|=2;var r=Gu(),l=Yu();iu!==e||uu!==t?(Eu=null,Cu=le()+500,Qu(e,t)):mu=Ne(e,t);e:for(;;)try{if(0!==cu&&null!==ou){t=ou;var s=du;t:switch(cu){case 1:cu=0,du=null,ac(e,t,s,1);break;case 2:case 9:if(Jr(s)){cu=0,du=null,nc(t);break}t=function(){2!==cu&&9!==cu||iu!==e||(cu=7),Cc(e)},s.then(t,t);break e;case 3:cu=7;break e;case 4:cu=5;break e;case 7:Jr(s)?(cu=0,du=null,nc(t)):(cu=0,du=null,ac(e,t,s,7));break;case 5:var i=null;switch(ou.tag){case 26:i=ou.memoizedState;case 5:case 27:var o=ou;if(!i||qd(i)){cu=0,du=null;var u=o.sibling;if(null!==u)ou=u;else{var c=o.return;null!==c?(ou=c,rc(c)):ou=null}break t}}cu=0,du=null,ac(e,t,s,5);break;case 6:cu=0,du=null,ac(e,t,s,6);break;case 8:Wu(),gu=6;break e;default:throw Error(a(462))}}ec();break}catch(d){Zu(e,d)}return Sr=wr=null,A.H=r,A.A=l,su=n,null!==ou?0:(iu=null,uu=0,_a(),gu)}(e,t):Xu(e,t,!0),s=r;;){if(0===l){mu&&!r&&Hu(e,t,0,!1);break}if(n=e.current.alternate,!s||Bu(n)){if(2===l){if(s=t,e.errorRecoveryDisabledLanes&s)var i=0;else i=0!==(i=-536870913&e.pendingLanes)?i:536870912&i?536870912:0;if(0!==i){t=i;e:{var o=e;l=Su;var u=o.current.memoizedState.isDehydrated;if(u&&(Qu(o,i).flags|=256),2!==(i=Xu(o,i,!1))){if(pu&&!u){o.errorRecoveryDisabledLanes|=s,vu|=s,l=4;break e}s=ku,ku=l,null!==s&&(null===ku?ku=s:ku.push.apply(ku,s))}l=i}if(s=!1,2!==l)continue}}if(1===l){Qu(e,0),Hu(e,t,0,!0);break}e:{switch(r=e,s=l){case 0:case 1:throw Error(a(345));case 4:if((4194048&t)!==t)break;case 6:Hu(r,t,yu,!fu);break e;case 2:ku=null;break;case 3:case 5:break;default:throw Error(a(329))}if((62914560&t)===t&&10<(l=ju+300-le())){if(Hu(r,t,yu,!fu),0!==ke(r,0,!0))break e;r.timeoutHandle=fd(Vu.bind(null,r,n,ku,Eu,Nu,t,yu,vu,wu,fu,s,2,-0,0),l)}else Vu(r,n,ku,Eu,Nu,t,yu,vu,wu,fu,s,0,-0,0)}break}l=Xu(e,t,!1),s=!1}Cc(e)}function Vu(e,t,n,r,l,s,i,o,u,c,d,f,m,p){if(e.timeoutHandle=-1,(8192&(f=t.subtreeFlags)||!(16785408&~f))&&(Wd={stylesheets:null,count:0,unsuspend:Qd},Xo(t),null!==(f=function(){if(null===Wd)throw Error(a(475));var e=Wd;return e.stylesheets&&0===e.count&&Yd(e,e.stylesheets),0<e.count?function(t){var n=setTimeout((function(){if(e.stylesheets&&Yd(e,e.stylesheets),e.unsuspend){var t=e.unsuspend;e.unsuspend=null,t()}}),6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}())))return e.cancelPendingCommit=f(sc.bind(null,e,t,s,n,r,l,i,o,u,d,1,m,p)),void Hu(e,s,i,!c);sc(e,t,s,n,r,l,i,o,u)}function Bu(e){for(var t=e;;){var n=t.tag;if((0===n||11===n||15===n)&&16384&t.flags&&(null!==(n=t.updateQueue)&&null!==(n=n.stores)))for(var a=0;a<n.length;a++){var r=n[a],l=r.getSnapshot;r=r.value;try{if(!Jn(l(),r))return!1}catch(s){return!1}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Hu(e,t,n,a){t&=~xu,t&=~vu,e.suspendedLanes|=t,e.pingedLanes&=~t,a&&(e.warmLanes|=t),a=e.expirationTimes;for(var r=t;0<r;){var l=31-be(r),s=1<<l;a[l]=-1,r&=~s}0!==n&&ze(e,n,t)}function qu(){return!!(6&su)||(Ec(0),!1)}function Wu(){if(null!==ou){if(0===cu)var e=ou.return;else Sr=wr=null,Ul(e=ou),Xs=null,Js=0,e=ou;for(;null!==e;)oo(e.alternate,e),e=e.return;ou=null}}function Qu(e,t){var n=e.timeoutHandle;-1!==n&&(e.timeoutHandle=-1,md(n)),null!==(n=e.cancelPendingCommit)&&(e.cancelPendingCommit=null,n()),Wu(),iu=e,ou=n=Va(e.current,null),uu=t,cu=0,du=null,fu=!1,mu=Ne(e,t),pu=!1,wu=yu=xu=vu=bu=gu=0,ku=Su=null,Nu=!1,8&t&&(t|=32&t);var a=e.entangledLanes;if(0!==a)for(e=e.entanglements,a&=t;0<a;){var r=31-be(a),l=1<<r;t|=e[r],a&=~l}return hu=t,_a(),n}function Zu(e,t){kl=null,A.H=Zs,t===Gr||t===Kr?(t=al(),cu=3):t===Yr?(t=al(),cu=4):cu=t===Pi?8:null!==t&&"object"==typeof t&&"function"==typeof t.then?6:1,du=t,null===ou&&(gu=1,Ni(e,Ta(t,e.current)))}function Gu(){var e=A.H;return A.H=Zs,null===e?Zs:e}function Yu(){var e=A.A;return A.A=ru,e}function Ku(){gu=4,fu||(4194048&uu)!==uu&&null!==ii.current||(mu=!0),!(134217727&bu)&&!(134217727&vu)||null===iu||Hu(iu,uu,yu,!1)}function Xu(e,t,n){var a=su;su|=2;var r=Gu(),l=Yu();iu===e&&uu===t||(Eu=null,Qu(e,t)),t=!1;var s=gu;e:for(;;)try{if(0!==cu&&null!==ou){var i=ou,o=du;switch(cu){case 8:Wu(),s=6;break e;case 3:case 2:case 9:case 6:null===ii.current&&(t=!0);var u=cu;if(cu=0,du=null,ac(e,i,o,u),n&&mu){s=0;break e}break;default:u=cu,cu=0,du=null,ac(e,i,o,u)}}Ju(),s=gu;break}catch(c){Zu(e,c)}return t&&e.shellSuspendCounter++,Sr=wr=null,su=a,A.H=r,A.A=l,null===ou&&(iu=null,uu=0,_a()),s}function Ju(){for(;null!==ou;)tc(ou)}function ec(){for(;null!==ou&&!ae();)tc(ou)}function tc(e){var t=eo(e.alternate,e,hu);e.memoizedProps=e.pendingProps,null===t?rc(e):ou=t}function nc(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=Ii(n,t,t.pendingProps,t.type,void 0,uu);break;case 11:t=Ii(n,t,t.pendingProps,t.type.render,t.ref,uu);break;case 5:Ul(t);default:oo(n,t),t=eo(n,t=ou=Ba(t,hu),hu)}e.memoizedProps=e.pendingProps,null===t?rc(e):ou=t}function ac(e,t,n,r){Sr=wr=null,Ul(t),Xs=null,Js=0;var l=t.return;try{if(function(e,t,n,r,l){if(n.flags|=32768,null!==r&&"object"==typeof r&&"function"==typeof r.then){if(null!==(t=n.alternate)&&Er(t,n,l,!0),null!==(n=ii.current)){switch(n.tag){case 13:return null===oi?Ku():null===n.alternate&&0===gu&&(gu=3),n.flags&=-257,n.flags|=65536,n.lanes=l,r===Xr?n.flags|=16384:(null===(t=n.updateQueue)?n.updateQueue=new Set([r]):t.add(r),hc(e,r,l)),!1;case 22:return n.flags|=65536,r===Xr?n.flags|=16384:(null===(t=n.updateQueue)?(t={transitions:null,markerInstances:null,retryQueue:new Set([r])},n.updateQueue=t):null===(n=t.retryQueue)?t.retryQueue=new Set([r]):n.add(r),hc(e,r,l)),!1}throw Error(a(435,n.tag))}return hc(e,r,l),Ku(),!1}if(ur)return null!==(t=ii.current)?(!(65536&t.flags)&&(t.flags|=256),t.flags|=65536,t.lanes=l,r!==fr&&xr(Ta(e=Error(a(422),{cause:r}),n))):(r!==fr&&xr(Ta(t=Error(a(423),{cause:r}),n)),(e=e.current.alternate).flags|=65536,l&=-l,e.lanes|=l,r=Ta(r,n),dl(e,l=Ci(e.stateNode,r,l)),4!==gu&&(gu=2)),!1;var s=Error(a(520),{cause:r});if(s=Ta(s,n),null===Su?Su=[s]:Su.push(s),4!==gu&&(gu=2),null===t)return!0;r=Ta(r,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=l&-l,n.lanes|=e,dl(n,e=Ci(n.stateNode,r,e)),!1;case 1:if(t=n.type,s=n.stateNode,!(128&n.flags||"function"!=typeof t.getDerivedStateFromError&&(null===s||"function"!=typeof s.componentDidCatch||null!==Tu&&Tu.has(s))))return n.flags|=65536,l&=-l,n.lanes|=l,Ti(l=Ei(l),e,n,r),dl(n,l),!1}n=n.return}while(null!==n);return!1}(e,l,t,n,uu))return gu=1,Ni(e,Ta(n,e.current)),void(ou=null)}catch(s){if(null!==l)throw ou=l,s;return gu=1,Ni(e,Ta(n,e.current)),void(ou=null)}32768&t.flags?(ur||1===r?e=!0:mu||536870912&uu?e=!1:(fu=e=!0,(2===r||9===r||3===r||6===r)&&(null!==(r=ii.current)&&13===r.tag&&(r.flags|=16384))),lc(t,e)):rc(t)}function rc(e){var t=e;do{if(32768&t.flags)return void lc(t,fu);e=t.return;var n=so(t.alternate,t,hu);if(null!==n)return void(ou=n);if(null!==(t=t.sibling))return void(ou=t);ou=t=e}while(null!==t);0===gu&&(gu=5)}function lc(e,t){do{var n=io(e.alternate,e);if(null!==n)return n.flags&=32767,void(ou=n);if(null!==(n=e.return)&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&null!==(e=e.sibling))return void(ou=e);ou=e=n}while(null!==e);gu=6,ou=null}function sc(e,t,n,r,l,s,i,o,u){e.cancelPendingCommit=null;do{dc()}while(0!==Pu);if(6&su)throw Error(a(327));if(null!==t){if(t===e.current)throw Error(a(177));if(s=t.lanes|t.childLanes,function(e,t,n,a,r,l){var s=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var i=e.entanglements,o=e.expirationTimes,u=e.hiddenUpdates;for(n=s&~n;0<n;){var c=31-be(n),d=1<<c;i[c]=0,o[c]=-1;var f=u[c];if(null!==f)for(u[c]=null,c=0;c<f.length;c++){var m=f[c];null!==m&&(m.lane&=-536870913)}n&=~d}0!==a&&ze(e,a,0),0!==l&&0===r&&0!==e.tag&&(e.suspendedLanes|=l&~(s&~t))}(e,n,s|=La,i,o,u),e===iu&&(ou=iu=null,uu=0),Lu=t,zu=e,_u=n,Mu=s,Ou=l,Fu=r,10256&t.subtreeFlags||10256&t.flags?(e.callbackNode=null,e.callbackPriority=0,te(ue,(function(){return fc(),null}))):(e.callbackNode=null,e.callbackPriority=0),r=!!(13878&t.flags),13878&t.subtreeFlags||r){r=A.T,A.T=null,l=I.p,I.p=2,i=su,su|=4;try{!function(e,t){if(e=e.containerInfo,ld=sf,la(e=ra(e))){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var l=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch(g){n=null;break e}var i=0,o=-1,u=-1,c=0,d=0,f=e,m=null;t:for(;;){for(var p;f!==n||0!==l&&3!==f.nodeType||(o=i+l),f!==s||0!==r&&3!==f.nodeType||(u=i+r),3===f.nodeType&&(i+=f.nodeValue.length),null!==(p=f.firstChild);)m=f,f=p;for(;;){if(f===e)break t;if(m===n&&++c===l&&(o=i),m===s&&++d===r&&(u=i),null!==(p=f.nextSibling))break;m=(f=m).parentNode}f=p}n=-1===o||-1===u?null:{start:o,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(sd={focusedElem:e,selectionRange:n},sf=!1,Eo=t;null!==Eo;)if(e=(t=Eo).child,1024&t.subtreeFlags&&null!==e)e.return=t,Eo=e;else for(;null!==Eo;){switch(s=(t=Eo).alternate,e=t.flags,t.tag){case 0:case 11:case 15:case 5:case 26:case 27:case 6:case 4:case 17:break;case 1:if(1024&e&&null!==s){e=void 0,n=t,l=s.memoizedProps,s=s.memoizedState,r=n.stateNode;try{var h=xi(n.type,l,(n.elementType,n.type));e=r.getSnapshotBeforeUpdate(h,s),r.__reactInternalSnapshotBeforeUpdate=e}catch(b){pc(n,n.return,b)}}break;case 3:if(1024&e)if(9===(n=(e=t.stateNode.containerInfo).nodeType))xd(e);else if(1===n)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":xd(e);break;default:e.textContent=""}break;default:if(1024&e)throw Error(a(163))}if(null!==(e=t.sibling)){e.return=t.return,Eo=e;break}Eo=t.return}}(e,t)}finally{su=i,I.p=l,A.T=r}}Pu=1,ic(),oc(),uc()}}function ic(){if(1===Pu){Pu=0;var e=zu,t=Lu,n=!!(13878&t.flags);if(13878&t.subtreeFlags||n){n=A.T,A.T=null;var a=I.p;I.p=2;var r=su;su|=4;try{Ao(t,e);var l=sd,s=ra(e.containerInfo),i=l.focusedElem,o=l.selectionRange;if(s!==i&&i&&i.ownerDocument&&aa(i.ownerDocument.documentElement,i)){if(null!==o&&la(i)){var u=o.start,c=o.end;if(void 0===c&&(c=u),"selectionStart"in i)i.selectionStart=u,i.selectionEnd=Math.min(c,i.value.length);else{var d=i.ownerDocument||document,f=d&&d.defaultView||window;if(f.getSelection){var m=f.getSelection(),p=i.textContent.length,h=Math.min(o.start,p),g=void 0===o.end?h:Math.min(o.end,p);!m.extend&&h>g&&(s=g,g=h,h=s);var b=na(i,h),v=na(i,g);if(b&&v&&(1!==m.rangeCount||m.anchorNode!==b.node||m.anchorOffset!==b.offset||m.focusNode!==v.node||m.focusOffset!==v.offset)){var x=d.createRange();x.setStart(b.node,b.offset),m.removeAllRanges(),h>g?(m.addRange(x),m.extend(v.node,v.offset)):(x.setEnd(v.node,v.offset),m.addRange(x))}}}}for(d=[],m=i;m=m.parentNode;)1===m.nodeType&&d.push({element:m,left:m.scrollLeft,top:m.scrollTop});for("function"==typeof i.focus&&i.focus(),i=0;i<d.length;i++){var y=d[i];y.element.scrollLeft=y.left,y.element.scrollTop=y.top}}sf=!!ld,sd=ld=null}finally{su=r,I.p=a,A.T=n}}e.current=t,Pu=2}}function oc(){if(2===Pu){Pu=0;var e=zu,t=Lu,n=!!(8772&t.flags);if(8772&t.subtreeFlags||n){n=A.T,A.T=null;var a=I.p;I.p=2;var r=su;su|=4;try{To(e,t.alternate,t)}finally{su=r,I.p=a,A.T=n}}Pu=3}}function uc(){if(4===Pu||3===Pu){Pu=0,re();var e=zu,t=Lu,n=_u,a=Fu;10256&t.subtreeFlags||10256&t.flags?Pu=5:(Pu=0,Lu=zu=null,cc(e,e.pendingLanes));var r=e.pendingLanes;if(0===r&&(Tu=null),Me(n),t=t.stateNode,he&&"function"==typeof he.onCommitFiberRoot)try{he.onCommitFiberRoot(pe,t,void 0,!(128&~t.current.flags))}catch(o){}if(null!==a){t=A.T,r=I.p,I.p=2,A.T=null;try{for(var l=e.onRecoverableError,s=0;s<a.length;s++){var i=a[s];l(i.value,{componentStack:i.stack})}}finally{A.T=t,I.p=r}}3&_u&&dc(),Cc(e),r=e.pendingLanes,4194090&n&&42&r?e===Du?Ru++:(Ru=0,Du=e):Ru=0,Ec(0)}}function cc(e,t){0===(e.pooledCacheLanes&=t)&&(null!=(t=e.pooledCache)&&(e.pooledCache=null,Ar(t)))}function dc(e){return ic(),oc(),uc(),fc()}function fc(){if(5!==Pu)return!1;var e=zu,t=Mu;Mu=0;var n=Me(_u),r=A.T,l=I.p;try{I.p=32>n?32:n,A.T=null,n=Ou,Ou=null;var s=zu,i=_u;if(Pu=0,Lu=zu=null,_u=0,6&su)throw Error(a(331));var o=su;if(su|=4,tu(s.current),Qo(s,s.current,i,n),su=o,Ec(0,!1),he&&"function"==typeof he.onPostCommitFiberRoot)try{he.onPostCommitFiberRoot(pe,s)}catch(u){}return!0}finally{I.p=l,A.T=r,cc(e,t)}}function mc(e,t,n){t=Ta(n,t),null!==(e=ul(e,t=Ci(e.stateNode,t,2),2))&&(Pe(e,2),Cc(e))}function pc(e,t,n){if(3===e.tag)mc(e,e,n);else for(;null!==t;){if(3===t.tag){mc(t,e,n);break}if(1===t.tag){var a=t.stateNode;if("function"==typeof t.type.getDerivedStateFromError||"function"==typeof a.componentDidCatch&&(null===Tu||!Tu.has(a))){e=Ta(n,e),null!==(a=ul(t,n=Ei(2),2))&&(Ti(n,a,t,e),Pe(a,2),Cc(a));break}}t=t.return}}function hc(e,t,n){var a=e.pingCache;if(null===a){a=e.pingCache=new lu;var r=new Set;a.set(t,r)}else void 0===(r=a.get(t))&&(r=new Set,a.set(t,r));r.has(n)||(pu=!0,r.add(n),e=gc.bind(null,e,t,n),t.then(e,e))}function gc(e,t,n){var a=e.pingCache;null!==a&&a.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,iu===e&&(uu&n)===n&&(4===gu||3===gu&&(62914560&uu)===uu&&300>le()-ju?!(2&su)&&Qu(e,0):xu|=n,wu===uu&&(wu=0)),Cc(e)}function bc(e,t){0===t&&(t=Ee()),null!==(e=Fa(e,t))&&(Pe(e,t),Cc(e))}function vc(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),bc(e,n)}function xc(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,l=e.memoizedState;null!==l&&(n=l.retryLane);break;case 19:r=e.stateNode;break;case 22:r=e.stateNode._retryCache;break;default:throw Error(a(314))}null!==r&&r.delete(t),bc(e,n)}var yc=null,wc=null,Sc=!1,kc=!1,Nc=!1,jc=0;function Cc(e){e!==wc&&null===e.next&&(null===wc?yc=wc=e:wc=wc.next=e),kc=!0,Sc||(Sc=!0,hd((function(){6&su?te(ie,Tc):Pc()})))}function Ec(e,t){if(!Nc&&kc){Nc=!0;do{for(var n=!1,a=yc;null!==a;){if(0!==e){var r=a.pendingLanes;if(0===r)var l=0;else{var s=a.suspendedLanes,i=a.pingedLanes;l=(1<<31-be(42|e)+1)-1,l=201326741&(l&=r&~(s&~i))?201326741&l|1:l?2|l:0}0!==l&&(n=!0,_c(a,l))}else l=uu,!(3&(l=ke(a,a===iu?l:0,null!==a.cancelPendingCommit||-1!==a.timeoutHandle)))||Ne(a,l)||(n=!0,_c(a,l));a=a.next}}while(n);Nc=!1}}function Tc(){Pc()}function Pc(){kc=Sc=!1;var e=0;0!==jc&&(function(){var e=window.event;if(e&&"popstate"===e.type)return e!==dd&&(dd=e,!0);return dd=null,!1}()&&(e=jc),jc=0);for(var t=le(),n=null,a=yc;null!==a;){var r=a.next,l=zc(a,t);0===l?(a.next=null,null===n?yc=r:n.next=r,null===r&&(wc=n)):(n=a,(0!==e||3&l)&&(kc=!0)),a=r}Ec(e)}function zc(e,t){for(var n=e.suspendedLanes,a=e.pingedLanes,r=e.expirationTimes,l=-62914561&e.pendingLanes;0<l;){var s=31-be(l),i=1<<s,o=r[s];-1===o?0!==(i&n)&&0===(i&a)||(r[s]=je(i,t)):o<=t&&(e.expiredLanes|=i),l&=~i}if(n=uu,n=ke(e,e===(t=iu)?n:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle),a=e.callbackNode,0===n||e===t&&(2===cu||9===cu)||null!==e.cancelPendingCommit)return null!==a&&null!==a&&ne(a),e.callbackNode=null,e.callbackPriority=0;if(!(3&n)||Ne(e,n)){if((t=n&-n)===e.callbackPriority)return t;switch(null!==a&&ne(a),Me(n)){case 2:case 8:n=oe;break;case 32:default:n=ue;break;case 268435456:n=de}return a=Lc.bind(null,e),n=te(n,a),e.callbackPriority=t,e.callbackNode=n,t}return null!==a&&null!==a&&ne(a),e.callbackPriority=2,e.callbackNode=null,2}function Lc(e,t){if(0!==Pu&&5!==Pu)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(dc()&&e.callbackNode!==n)return null;var a=uu;return 0===(a=ke(e,e===iu?a:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle))?null:(Uu(e,a,t),zc(e,le()),null!=e.callbackNode&&e.callbackNode===n?Lc.bind(null,e):null)}function _c(e,t){if(dc())return null;Uu(e,t,!0)}function Mc(){return 0===jc&&(jc=Ce()),jc}function Oc(e){return null==e||"symbol"==typeof e||"boolean"==typeof e?null:"function"==typeof e?e:Mt(""+e)}function Fc(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}for(var Rc=0;Rc<ja.length;Rc++){var Dc=ja[Rc];Ca(Dc.toLowerCase(),"on"+(Dc[0].toUpperCase()+Dc.slice(1)))}Ca(ba,"onAnimationEnd"),Ca(va,"onAnimationIteration"),Ca(xa,"onAnimationStart"),Ca("dblclick","onDoubleClick"),Ca("focusin","onFocus"),Ca("focusout","onBlur"),Ca(ya,"onTransitionRun"),Ca(wa,"onTransitionStart"),Ca(Sa,"onTransitionCancel"),Ca(ka,"onTransitionEnd"),Je("onMouseEnter",["mouseout","mouseover"]),Je("onMouseLeave",["mouseout","mouseover"]),Je("onPointerEnter",["pointerout","pointerover"]),Je("onPointerLeave",["pointerout","pointerover"]),Xe("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Xe("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Xe("onBeforeInput",["compositionend","keypress","textInput","paste"]),Xe("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Xe("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Xe("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ac="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Ic=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Ac));function $c(e,t){t=!!(4&t);for(var n=0;n<e.length;n++){var a=e[n],r=a.event;a=a.listeners;e:{var l=void 0;if(t)for(var s=a.length-1;0<=s;s--){var i=a[s],o=i.instance,u=i.currentTarget;if(i=i.listener,o!==l&&r.isPropagationStopped())break e;l=i,r.currentTarget=u;try{l(r)}catch(c){yi(c)}r.currentTarget=null,l=o}else for(s=0;s<a.length;s++){if(o=(i=a[s]).instance,u=i.currentTarget,i=i.listener,o!==l&&r.isPropagationStopped())break e;l=i,r.currentTarget=u;try{l(r)}catch(c){yi(c)}r.currentTarget=null,l=o}}}}function Uc(e,t){var n=t[Ie];void 0===n&&(n=t[Ie]=new Set);var a=e+"__bubble";n.has(a)||(qc(t,e,2,!1),n.add(a))}function Vc(e,t,n){var a=0;t&&(a|=4),qc(n,e,a,t)}var Bc="_reactListening"+Math.random().toString(36).slice(2);function Hc(e){if(!e[Bc]){e[Bc]=!0,Ye.forEach((function(t){"selectionchange"!==t&&(Ic.has(t)||Vc(t,!1,e),Vc(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Bc]||(t[Bc]=!0,Vc("selectionchange",!1,t))}}function qc(e,t,n,a){switch(pf(t)){case 2:var r=of;break;case 8:r=uf;break;default:r=cf}n=r.bind(null,t,n,e),r=void 0,!Bt||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(r=!0),a?void 0!==r?e.addEventListener(t,n,{capture:!0,passive:r}):e.addEventListener(t,n,!0):void 0!==r?e.addEventListener(t,n,{passive:r}):e.addEventListener(t,n,!1)}function Wc(e,t,n,a,r){var s=a;if(!(1&t||2&t||null===a))e:for(;;){if(null===a)return;var i=a.tag;if(3===i||4===i){var o=a.stateNode.containerInfo;if(o===r)break;if(4===i)for(i=a.return;null!==i;){var u=i.tag;if((3===u||4===u)&&i.stateNode.containerInfo===r)return;i=i.return}for(;null!==o;){if(null===(i=qe(o)))return;if(5===(u=i.tag)||6===u||26===u||27===u){a=s=i;continue e}o=o.parentNode}}a=a.return}$t((function(){var a=s,r=Ft(n),i=[];e:{var o=Na.get(e);if(void 0!==o){var u=an,c=e;switch(e){case"keypress":if(0===Gt(n))break e;case"keydown":case"keyup":u=xn;break;case"focusin":c="focus",u=cn;break;case"focusout":c="blur",u=cn;break;case"beforeblur":case"afterblur":u=cn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":u=on;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":u=un;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":u=wn;break;case ba:case va:case xa:u=dn;break;case ka:u=Sn;break;case"scroll":case"scrollend":u=ln;break;case"wheel":u=kn;break;case"copy":case"cut":case"paste":u=fn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":u=yn;break;case"toggle":case"beforetoggle":u=Nn}var d=!!(4&t),f=!d&&("scroll"===e||"scrollend"===e),m=d?null!==o?o+"Capture":null:o;d=[];for(var p,h=a;null!==h;){var g=h;if(p=g.stateNode,5!==(g=g.tag)&&26!==g&&27!==g||null===p||null===m||null!=(g=Ut(h,m))&&d.push(Qc(h,g,p)),f)break;h=h.return}0<d.length&&(o=new u(o,c,null,n,r),i.push({event:o,listeners:d}))}}if(!(7&t)){if(u="mouseout"===e||"pointerout"===e,(!(o="mouseover"===e||"pointerover"===e)||n===Ot||!(c=n.relatedTarget||n.fromElement)||!qe(c)&&!c[Ae])&&(u||o)&&(o=r.window===r?r:(o=r.ownerDocument)?o.defaultView||o.parentWindow:window,u?(u=a,null!==(c=(c=n.relatedTarget||n.toElement)?qe(c):null)&&(f=l(c),d=c.tag,c!==f||5!==d&&27!==d&&6!==d)&&(c=null)):(u=null,c=a),u!==c)){if(d=on,g="onMouseLeave",m="onMouseEnter",h="mouse","pointerout"!==e&&"pointerover"!==e||(d=yn,g="onPointerLeave",m="onPointerEnter",h="pointer"),f=null==u?o:Qe(u),p=null==c?o:Qe(c),(o=new d(g,h+"leave",u,n,r)).target=f,o.relatedTarget=p,g=null,qe(r)===a&&((d=new d(m,h+"enter",c,n,r)).target=p,d.relatedTarget=f,g=d),f=g,u&&c)e:{for(m=c,h=0,p=d=u;p;p=Gc(p))h++;for(p=0,g=m;g;g=Gc(g))p++;for(;0<h-p;)d=Gc(d),h--;for(;0<p-h;)m=Gc(m),p--;for(;h--;){if(d===m||null!==m&&d===m.alternate)break e;d=Gc(d),m=Gc(m)}d=null}else d=null;null!==u&&Yc(i,o,u,d,!1),null!==c&&null!==f&&Yc(i,f,c,d,!0)}if("select"===(u=(o=a?Qe(a):window).nodeName&&o.nodeName.toLowerCase())||"input"===u&&"file"===o.type)var b=Vn;else if(Rn(o))if(Bn)b=Xn;else{b=Yn;var v=Gn}else!(u=o.nodeName)||"input"!==u.toLowerCase()||"checkbox"!==o.type&&"radio"!==o.type?a&&zt(a.elementType)&&(b=Vn):b=Kn;switch(b&&(b=b(e,a))?Dn(i,b,n,r):(v&&v(e,o,a),"focusout"===e&&a&&"number"===o.type&&null!=a.memoizedProps.value&&St(o,"number",o.value)),v=a?Qe(a):window,e){case"focusin":(Rn(v)||"true"===v.contentEditable)&&(ia=v,oa=a,ua=null);break;case"focusout":ua=oa=ia=null;break;case"mousedown":ca=!0;break;case"contextmenu":case"mouseup":case"dragend":ca=!1,da(i,n,r);break;case"selectionchange":if(sa)break;case"keydown":case"keyup":da(i,n,r)}var x;if(Cn)e:{switch(e){case"compositionstart":var y="onCompositionStart";break e;case"compositionend":y="onCompositionEnd";break e;case"compositionupdate":y="onCompositionUpdate";break e}y=void 0}else On?_n(e,n)&&(y="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(y="onCompositionStart");y&&(Pn&&"ko"!==n.locale&&(On||"onCompositionStart"!==y?"onCompositionEnd"===y&&On&&(x=Zt()):(Wt="value"in(qt=r)?qt.value:qt.textContent,On=!0)),0<(v=Zc(a,y)).length&&(y=new mn(y,e,null,n,r),i.push({event:y,listeners:v}),x?y.data=x:null!==(x=Mn(n))&&(y.data=x))),(x=Tn?function(e,t){switch(e){case"compositionend":return Mn(t);case"keypress":return 32!==t.which?null:(Ln=!0,zn);case"textInput":return(e=t.data)===zn&&Ln?null:e;default:return null}}(e,n):function(e,t){if(On)return"compositionend"===e||!Cn&&_n(e,t)?(e=Zt(),Qt=Wt=qt=null,On=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Pn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(y=Zc(a,"onBeforeInput")).length&&(v=new mn("onBeforeInput","beforeinput",null,n,r),i.push({event:v,listeners:y}),v.data=x)),function(e,t,n,a,r){if("submit"===t&&n&&n.stateNode===r){var l=Oc((r[De]||null).action),s=a.submitter;s&&null!==(t=(t=s[De]||null)?Oc(t.formAction):s.getAttribute("formAction"))&&(l=t,s=null);var i=new an("action","action",null,a,r);e.push({event:i,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(0!==jc){var e=s?Fc(r,s):new FormData(r);Os(n,{pending:!0,data:e,method:r.method,action:l},null,e)}}else"function"==typeof l&&(i.preventDefault(),e=s?Fc(r,s):new FormData(r),Os(n,{pending:!0,data:e,method:r.method,action:l},l,e))},currentTarget:r}]})}}(i,e,a,n,r)}$c(i,t)}))}function Qc(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Zc(e,t){for(var n=t+"Capture",a=[];null!==e;){var r=e,l=r.stateNode;if(5!==(r=r.tag)&&26!==r&&27!==r||null===l||(null!=(r=Ut(e,n))&&a.unshift(Qc(e,r,l)),null!=(r=Ut(e,t))&&a.push(Qc(e,r,l))),3===e.tag)return a;e=e.return}return[]}function Gc(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag&&27!==e.tag);return e||null}function Yc(e,t,n,a,r){for(var l=t._reactName,s=[];null!==n&&n!==a;){var i=n,o=i.alternate,u=i.stateNode;if(i=i.tag,null!==o&&o===a)break;5!==i&&26!==i&&27!==i||null===u||(o=u,r?null!=(u=Ut(n,l))&&s.unshift(Qc(n,u,o)):r||null!=(u=Ut(n,l))&&s.push(Qc(n,u,o))),n=n.return}0!==s.length&&e.push({event:t,listeners:s})}var Kc=/\r\n?/g,Xc=/\u0000|\uFFFD/g;function Jc(e){return("string"==typeof e?e:""+e).replace(Kc,"\n").replace(Xc,"")}function ed(e,t){return t=Jc(t),Jc(e)===t}function td(){}function nd(e,t,n,r,l,s){switch(n){case"children":"string"==typeof r?"body"===t||"textarea"===t&&""===r||Ct(e,r):("number"==typeof r||"bigint"==typeof r)&&"body"!==t&&Ct(e,""+r);break;case"className":st(e,"class",r);break;case"tabIndex":st(e,"tabindex",r);break;case"dir":case"role":case"viewBox":case"width":case"height":st(e,n,r);break;case"style":Pt(e,r,s);break;case"data":if("object"!==t){st(e,"data",r);break}case"src":case"href":if(""===r&&("a"!==t||"href"!==n)){e.removeAttribute(n);break}if(null==r||"function"==typeof r||"symbol"==typeof r||"boolean"==typeof r){e.removeAttribute(n);break}r=Mt(""+r),e.setAttribute(n,r);break;case"action":case"formAction":if("function"==typeof r){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}if("function"==typeof s&&("formAction"===n?("input"!==t&&nd(e,t,"name",l.name,l,null),nd(e,t,"formEncType",l.formEncType,l,null),nd(e,t,"formMethod",l.formMethod,l,null),nd(e,t,"formTarget",l.formTarget,l,null)):(nd(e,t,"encType",l.encType,l,null),nd(e,t,"method",l.method,l,null),nd(e,t,"target",l.target,l,null))),null==r||"symbol"==typeof r||"boolean"==typeof r){e.removeAttribute(n);break}r=Mt(""+r),e.setAttribute(n,r);break;case"onClick":null!=r&&(e.onclick=td);break;case"onScroll":null!=r&&Uc("scroll",e);break;case"onScrollEnd":null!=r&&Uc("scrollend",e);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!=typeof r||!("__html"in r))throw Error(a(61));if(null!=(n=r.__html)){if(null!=l.children)throw Error(a(60));e.innerHTML=n}}break;case"multiple":e.multiple=r&&"function"!=typeof r&&"symbol"!=typeof r;break;case"muted":e.muted=r&&"function"!=typeof r&&"symbol"!=typeof r;break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":case"autoFocus":break;case"xlinkHref":if(null==r||"function"==typeof r||"boolean"==typeof r||"symbol"==typeof r){e.removeAttribute("xlink:href");break}n=Mt(""+r),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":null!=r&&"function"!=typeof r&&"symbol"!=typeof r?e.setAttribute(n,""+r):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":r&&"function"!=typeof r&&"symbol"!=typeof r?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":!0===r?e.setAttribute(n,""):!1!==r&&null!=r&&"function"!=typeof r&&"symbol"!=typeof r?e.setAttribute(n,r):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":null!=r&&"function"!=typeof r&&"symbol"!=typeof r&&!isNaN(r)&&1<=r?e.setAttribute(n,r):e.removeAttribute(n);break;case"rowSpan":case"start":null==r||"function"==typeof r||"symbol"==typeof r||isNaN(r)?e.removeAttribute(n):e.setAttribute(n,r);break;case"popover":Uc("beforetoggle",e),Uc("toggle",e),lt(e,"popover",r);break;case"xlinkActuate":it(e,"http://www.w3.org/1999/xlink","xlink:actuate",r);break;case"xlinkArcrole":it(e,"http://www.w3.org/1999/xlink","xlink:arcrole",r);break;case"xlinkRole":it(e,"http://www.w3.org/1999/xlink","xlink:role",r);break;case"xlinkShow":it(e,"http://www.w3.org/1999/xlink","xlink:show",r);break;case"xlinkTitle":it(e,"http://www.w3.org/1999/xlink","xlink:title",r);break;case"xlinkType":it(e,"http://www.w3.org/1999/xlink","xlink:type",r);break;case"xmlBase":it(e,"http://www.w3.org/XML/1998/namespace","xml:base",r);break;case"xmlLang":it(e,"http://www.w3.org/XML/1998/namespace","xml:lang",r);break;case"xmlSpace":it(e,"http://www.w3.org/XML/1998/namespace","xml:space",r);break;case"is":lt(e,"is",r);break;case"innerText":case"textContent":break;default:(!(2<n.length)||"o"!==n[0]&&"O"!==n[0]||"n"!==n[1]&&"N"!==n[1])&&lt(e,n=Lt.get(n)||n,r)}}function ad(e,t,n,r,l,s){switch(n){case"style":Pt(e,r,s);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!=typeof r||!("__html"in r))throw Error(a(61));if(null!=(n=r.__html)){if(null!=l.children)throw Error(a(60));e.innerHTML=n}}break;case"children":"string"==typeof r?Ct(e,r):("number"==typeof r||"bigint"==typeof r)&&Ct(e,""+r);break;case"onScroll":null!=r&&Uc("scroll",e);break;case"onScrollEnd":null!=r&&Uc("scrollend",e);break;case"onClick":null!=r&&(e.onclick=td);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":case"innerText":case"textContent":break;default:Ke.hasOwnProperty(n)||("o"!==n[0]||"n"!==n[1]||(l=n.endsWith("Capture"),t=n.slice(2,l?n.length-7:void 0),"function"==typeof(s=null!=(s=e[De]||null)?s[n]:null)&&e.removeEventListener(t,s,l),"function"!=typeof r)?n in e?e[n]=r:!0===r?e.setAttribute(n,""):lt(e,n,r):("function"!=typeof s&&null!==s&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,r,l)))}}function rd(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Uc("error",e),Uc("load",e);var r,l=!1,s=!1;for(r in n)if(n.hasOwnProperty(r)){var i=n[r];if(null!=i)switch(r){case"src":l=!0;break;case"srcSet":s=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(a(137,t));default:nd(e,t,r,i,n,null)}}return s&&nd(e,t,"srcSet",n.srcSet,n,null),void(l&&nd(e,t,"src",n.src,n,null));case"input":Uc("invalid",e);var o=r=i=s=null,u=null,c=null;for(l in n)if(n.hasOwnProperty(l)){var d=n[l];if(null!=d)switch(l){case"name":s=d;break;case"type":i=d;break;case"checked":u=d;break;case"defaultChecked":c=d;break;case"value":r=d;break;case"defaultValue":o=d;break;case"children":case"dangerouslySetInnerHTML":if(null!=d)throw Error(a(137,t));break;default:nd(e,t,l,d,n,null)}}return wt(e,r,o,u,c,i,s,!1),void ht(e);case"select":for(s in Uc("invalid",e),l=i=r=null,n)if(n.hasOwnProperty(s)&&null!=(o=n[s]))switch(s){case"value":r=o;break;case"defaultValue":i=o;break;case"multiple":l=o;default:nd(e,t,s,o,n,null)}return t=r,n=i,e.multiple=!!l,void(null!=t?kt(e,!!l,t,!1):null!=n&&kt(e,!!l,n,!0));case"textarea":for(i in Uc("invalid",e),r=s=l=null,n)if(n.hasOwnProperty(i)&&null!=(o=n[i]))switch(i){case"value":l=o;break;case"defaultValue":s=o;break;case"children":r=o;break;case"dangerouslySetInnerHTML":if(null!=o)throw Error(a(91));break;default:nd(e,t,i,o,n,null)}return jt(e,l,s,r),void ht(e);case"option":for(u in n)if(n.hasOwnProperty(u)&&null!=(l=n[u]))if("selected"===u)e.selected=l&&"function"!=typeof l&&"symbol"!=typeof l;else nd(e,t,u,l,n,null);return;case"dialog":Uc("beforetoggle",e),Uc("toggle",e),Uc("cancel",e),Uc("close",e);break;case"iframe":case"object":Uc("load",e);break;case"video":case"audio":for(l=0;l<Ac.length;l++)Uc(Ac[l],e);break;case"image":Uc("error",e),Uc("load",e);break;case"details":Uc("toggle",e);break;case"embed":case"source":case"link":Uc("error",e),Uc("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(c in n)if(n.hasOwnProperty(c)&&null!=(l=n[c]))switch(c){case"children":case"dangerouslySetInnerHTML":throw Error(a(137,t));default:nd(e,t,c,l,n,null)}return;default:if(zt(t)){for(d in n)n.hasOwnProperty(d)&&(void 0!==(l=n[d])&&ad(e,t,d,l,n,void 0));return}}for(o in n)n.hasOwnProperty(o)&&(null!=(l=n[o])&&nd(e,t,o,l,n,null))}var ld=null,sd=null;function id(e){return 9===e.nodeType?e:e.ownerDocument}function od(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function ud(e,t){if(0===e)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return 1===e&&"foreignObject"===t?0:e}function cd(e,t){return"textarea"===e||"noscript"===e||"string"==typeof t.children||"number"==typeof t.children||"bigint"==typeof t.children||"object"==typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var dd=null;var fd="function"==typeof setTimeout?setTimeout:void 0,md="function"==typeof clearTimeout?clearTimeout:void 0,pd="function"==typeof Promise?Promise:void 0,hd="function"==typeof queueMicrotask?queueMicrotask:void 0!==pd?function(e){return pd.resolve(null).then(e).catch(gd)}:fd;function gd(e){setTimeout((function(){throw e}))}function bd(e){return"head"===e}function vd(e,t){var n=t,a=0,r=0;do{var l=n.nextSibling;if(e.removeChild(n),l&&8===l.nodeType)if("/$"===(n=l.data)){if(0<a&&8>a){n=a;var s=e.ownerDocument;if(1&n&&jd(s.documentElement),2&n&&jd(s.body),4&n)for(jd(n=s.head),s=n.firstChild;s;){var i=s.nextSibling,o=s.nodeName;s[Be]||"SCRIPT"===o||"STYLE"===o||"LINK"===o&&"stylesheet"===s.rel.toLowerCase()||n.removeChild(s),s=i}}if(0===r)return e.removeChild(l),void _f(t);r--}else"$"===n||"$?"===n||"$!"===n?r++:a=n.charCodeAt(0)-48;else a=0;n=l}while(n);_f(t)}function xd(e){var t=e.firstChild;for(t&&10===t.nodeType&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":xd(n),He(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if("stylesheet"===n.rel.toLowerCase())continue}e.removeChild(n)}}function yd(e){return"$!"===e.data||"$?"===e.data&&"complete"===e.ownerDocument.readyState}function wd(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t||"F!"===t||"F"===t)break;if("/$"===t)return null}}return e}var Sd=null;function kd(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}function Nd(e,t,n){switch(t=id(n),e){case"html":if(!(e=t.documentElement))throw Error(a(452));return e;case"head":if(!(e=t.head))throw Error(a(453));return e;case"body":if(!(e=t.body))throw Error(a(454));return e;default:throw Error(a(451))}}function jd(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);He(e)}var Cd=new Map,Ed=new Set;function Td(e){return"function"==typeof e.getRootNode?e.getRootNode():9===e.nodeType?e:e.ownerDocument}var Pd=I.d;I.d={f:function(){var e=Pd.f(),t=qu();return e||t},r:function(e){var t=We(e);null!==t&&5===t.tag&&"form"===t.type?Rs(t):Pd.r(e)},D:function(e){Pd.D(e),Ld("dns-prefetch",e,null)},C:function(e,t){Pd.C(e,t),Ld("preconnect",e,t)},L:function(e,t,n){Pd.L(e,t,n);var a=zd;if(a&&e&&t){var r='link[rel="preload"][as="'+xt(t)+'"]';"image"===t&&n&&n.imageSrcSet?(r+='[imagesrcset="'+xt(n.imageSrcSet)+'"]',"string"==typeof n.imageSizes&&(r+='[imagesizes="'+xt(n.imageSizes)+'"]')):r+='[href="'+xt(e)+'"]';var l=r;switch(t){case"style":l=Md(e);break;case"script":l=Rd(e)}Cd.has(l)||(e=u({rel:"preload",href:"image"===t&&n&&n.imageSrcSet?void 0:e,as:t},n),Cd.set(l,e),null!==a.querySelector(r)||"style"===t&&a.querySelector(Od(l))||"script"===t&&a.querySelector(Dd(l))||(rd(t=a.createElement("link"),"link",e),Ge(t),a.head.appendChild(t)))}},m:function(e,t){Pd.m(e,t);var n=zd;if(n&&e){var a=t&&"string"==typeof t.as?t.as:"script",r='link[rel="modulepreload"][as="'+xt(a)+'"][href="'+xt(e)+'"]',l=r;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":l=Rd(e)}if(!Cd.has(l)&&(e=u({rel:"modulepreload",href:e},t),Cd.set(l,e),null===n.querySelector(r))){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Dd(l)))return}rd(a=n.createElement("link"),"link",e),Ge(a),n.head.appendChild(a)}}},X:function(e,t){Pd.X(e,t);var n=zd;if(n&&e){var a=Ze(n).hoistableScripts,r=Rd(e),l=a.get(r);l||((l=n.querySelector(Dd(r)))||(e=u({src:e,async:!0},t),(t=Cd.get(r))&&Ud(e,t),Ge(l=n.createElement("script")),rd(l,"link",e),n.head.appendChild(l)),l={type:"script",instance:l,count:1,state:null},a.set(r,l))}},S:function(e,t,n){Pd.S(e,t,n);var a=zd;if(a&&e){var r=Ze(a).hoistableStyles,l=Md(e);t=t||"default";var s=r.get(l);if(!s){var i={loading:0,preload:null};if(s=a.querySelector(Od(l)))i.loading=5;else{e=u({rel:"stylesheet",href:e,"data-precedence":t},n),(n=Cd.get(l))&&$d(e,n);var o=s=a.createElement("link");Ge(o),rd(o,"link",e),o._p=new Promise((function(e,t){o.onload=e,o.onerror=t})),o.addEventListener("load",(function(){i.loading|=1})),o.addEventListener("error",(function(){i.loading|=2})),i.loading|=4,Id(s,t,a)}s={type:"stylesheet",instance:s,count:1,state:i},r.set(l,s)}}},M:function(e,t){Pd.M(e,t);var n=zd;if(n&&e){var a=Ze(n).hoistableScripts,r=Rd(e),l=a.get(r);l||((l=n.querySelector(Dd(r)))||(e=u({src:e,async:!0,type:"module"},t),(t=Cd.get(r))&&Ud(e,t),Ge(l=n.createElement("script")),rd(l,"link",e),n.head.appendChild(l)),l={type:"script",instance:l,count:1,state:null},a.set(r,l))}}};var zd="undefined"==typeof document?null:document;function Ld(e,t,n){var a=zd;if(a&&"string"==typeof t&&t){var r=xt(t);r='link[rel="'+e+'"][href="'+r+'"]',"string"==typeof n&&(r+='[crossorigin="'+n+'"]'),Ed.has(r)||(Ed.add(r),e={rel:e,crossOrigin:n,href:t},null===a.querySelector(r)&&(rd(t=a.createElement("link"),"link",e),Ge(t),a.head.appendChild(t)))}}function _d(e,t,n,r){var l,s,i,o,u=(u=Z.current)?Td(u):null;if(!u)throw Error(a(446));switch(e){case"meta":case"title":return null;case"style":return"string"==typeof n.precedence&&"string"==typeof n.href?(t=Md(n.href),(r=(n=Ze(u).hoistableStyles).get(t))||(r={type:"style",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};case"link":if("stylesheet"===n.rel&&"string"==typeof n.href&&"string"==typeof n.precedence){e=Md(n.href);var c=Ze(u).hoistableStyles,d=c.get(e);if(d||(u=u.ownerDocument||u,d={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(e,d),(c=u.querySelector(Od(e)))&&!c._p&&(d.instance=c,d.state.loading=5),Cd.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Cd.set(e,n),c||(l=u,s=e,i=n,o=d.state,l.querySelector('link[rel="preload"][as="style"]['+s+"]")?o.loading=1:(s=l.createElement("link"),o.preload=s,s.addEventListener("load",(function(){return o.loading|=1})),s.addEventListener("error",(function(){return o.loading|=2})),rd(s,"link",i),Ge(s),l.head.appendChild(s))))),t&&null===r)throw Error(a(528,""));return d}if(t&&null!==r)throw Error(a(529,""));return null;case"script":return t=n.async,"string"==typeof(n=n.src)&&t&&"function"!=typeof t&&"symbol"!=typeof t?(t=Rd(n),(r=(n=Ze(u).hoistableScripts).get(t))||(r={type:"script",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};default:throw Error(a(444,e))}}function Md(e){return'href="'+xt(e)+'"'}function Od(e){return'link[rel="stylesheet"]['+e+"]"}function Fd(e){return u({},e,{"data-precedence":e.precedence,precedence:null})}function Rd(e){return'[src="'+xt(e)+'"]'}function Dd(e){return"script[async]"+e}function Ad(e,t,n){if(t.count++,null===t.instance)switch(t.type){case"style":var r=e.querySelector('style[data-href~="'+xt(n.href)+'"]');if(r)return t.instance=r,Ge(r),r;var l=u({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return Ge(r=(e.ownerDocument||e).createElement("style")),rd(r,"style",l),Id(r,n.precedence,e),t.instance=r;case"stylesheet":l=Md(n.href);var s=e.querySelector(Od(l));if(s)return t.state.loading|=4,t.instance=s,Ge(s),s;r=Fd(n),(l=Cd.get(l))&&$d(r,l),Ge(s=(e.ownerDocument||e).createElement("link"));var i=s;return i._p=new Promise((function(e,t){i.onload=e,i.onerror=t})),rd(s,"link",r),t.state.loading|=4,Id(s,n.precedence,e),t.instance=s;case"script":return s=Rd(n.src),(l=e.querySelector(Dd(s)))?(t.instance=l,Ge(l),l):(r=n,(l=Cd.get(s))&&Ud(r=u({},n),l),Ge(l=(e=e.ownerDocument||e).createElement("script")),rd(l,"link",r),e.head.appendChild(l),t.instance=l);case"void":return null;default:throw Error(a(443,t.type))}else"stylesheet"===t.type&&!(4&t.state.loading)&&(r=t.instance,t.state.loading|=4,Id(r,n.precedence,e));return t.instance}function Id(e,t,n){for(var a=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),r=a.length?a[a.length-1]:null,l=r,s=0;s<a.length;s++){var i=a[s];if(i.dataset.precedence===t)l=i;else if(l!==r)break}l?l.parentNode.insertBefore(e,l.nextSibling):(t=9===n.nodeType?n.head:n).insertBefore(e,t.firstChild)}function $d(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.title&&(e.title=t.title)}function Ud(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.integrity&&(e.integrity=t.integrity)}var Vd=null;function Bd(e,t,n){if(null===Vd){var a=new Map,r=Vd=new Map;r.set(n,a)}else(a=(r=Vd).get(n))||(a=new Map,r.set(n,a));if(a.has(e))return a;for(a.set(e,null),n=n.getElementsByTagName(e),r=0;r<n.length;r++){var l=n[r];if(!(l[Be]||l[Re]||"link"===e&&"stylesheet"===l.getAttribute("rel"))&&"http://www.w3.org/2000/svg"!==l.namespaceURI){var s=l.getAttribute(t)||"";s=e+s;var i=a.get(s);i?i.push(l):a.set(s,[l])}}return a}function Hd(e,t,n){(e=e.ownerDocument||e).head.insertBefore(n,"title"===t?e.querySelector("head > title"):null)}function qd(e){return!!("stylesheet"!==e.type||3&e.state.loading)}var Wd=null;function Qd(){}function Zd(){if(this.count--,0===this.count)if(this.stylesheets)Yd(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}var Gd=null;function Yd(e,t){e.stylesheets=null,null!==e.unsuspend&&(e.count++,Gd=new Map,t.forEach(Kd,e),Gd=null,Zd.call(e))}function Kd(e,t){if(!(4&t.state.loading)){var n=Gd.get(e);if(n)var a=n.get(null);else{n=new Map,Gd.set(e,n);for(var r=e.querySelectorAll("link[data-precedence],style[data-precedence]"),l=0;l<r.length;l++){var s=r[l];"LINK"!==s.nodeName&&"not all"===s.getAttribute("media")||(n.set(s.dataset.precedence,s),a=s)}a&&n.set(null,a)}s=(r=t.instance).getAttribute("data-precedence"),(l=n.get(s)||a)===a&&n.set(null,r),n.set(s,r),this.count++,a=Zd.bind(this),r.addEventListener("load",a),r.addEventListener("error",a),l?l.parentNode.insertBefore(r,l.nextSibling):(e=9===e.nodeType?e.head:e).insertBefore(r,e.firstChild),t.state.loading|=4}}var Xd={$$typeof:w,Provider:null,Consumer:null,_currentValue:$,_currentValue2:$,_threadCount:0};function Jd(e,t,n,a,r,l,s,i){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Te(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Te(0),this.hiddenUpdates=Te(null),this.identifierPrefix=a,this.onUncaughtError=r,this.onCaughtError=l,this.onRecoverableError=s,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=i,this.incompleteTransitions=new Map}function ef(e,t,n,a,r,l,s,i,o,u,c,d){return e=new Jd(e,t,n,s,i,o,u,d),t=1,!0===l&&(t|=24),l=$a(3,null,null,t),e.current=l,l.stateNode=e,(t=Dr()).refCount++,e.pooledCache=t,t.refCount++,l.memoizedState={element:a,isDehydrated:n,cache:t},sl(l),e}function tf(e){return e?e=Aa:Aa}function nf(e,t,n,a,r,l){r=tf(r),null===a.context?a.context=r:a.pendingContext=r,(a=ol(t)).payload={element:n},null!==(l=void 0===l?null:l)&&(a.callback=l),null!==(n=ul(e,a,t))&&($u(n,0,t),cl(n,e,t))}function af(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function rf(e,t){af(e,t),(e=e.alternate)&&af(e,t)}function lf(e){if(13===e.tag){var t=Fa(e,67108864);null!==t&&$u(t,0,67108864),rf(e,67108864)}}var sf=!0;function of(e,t,n,a){var r=A.T;A.T=null;var l=I.p;try{I.p=2,cf(e,t,n,a)}finally{I.p=l,A.T=r}}function uf(e,t,n,a){var r=A.T;A.T=null;var l=I.p;try{I.p=8,cf(e,t,n,a)}finally{I.p=l,A.T=r}}function cf(e,t,n,a){if(sf){var r=df(a);if(null===r)Wc(e,t,a,ff,n),kf(e,a);else if(function(e,t,n,a,r){switch(t){case"focusin":return gf=Nf(gf,e,t,n,a,r),!0;case"dragenter":return bf=Nf(bf,e,t,n,a,r),!0;case"mouseover":return vf=Nf(vf,e,t,n,a,r),!0;case"pointerover":var l=r.pointerId;return xf.set(l,Nf(xf.get(l)||null,e,t,n,a,r)),!0;case"gotpointercapture":return l=r.pointerId,yf.set(l,Nf(yf.get(l)||null,e,t,n,a,r)),!0}return!1}(r,e,t,n,a))a.stopPropagation();else if(kf(e,a),4&t&&-1<Sf.indexOf(e)){for(;null!==r;){var l=We(r);if(null!==l)switch(l.tag){case 3:if((l=l.stateNode).current.memoizedState.isDehydrated){var s=Se(l.pendingLanes);if(0!==s){var i=l;for(i.pendingLanes|=2,i.entangledLanes|=2;s;){var o=1<<31-be(s);i.entanglements[1]|=o,s&=~o}Cc(l),!(6&su)&&(Cu=le()+500,Ec(0))}}break;case 13:null!==(i=Fa(l,2))&&$u(i,0,2),qu(),rf(l,2)}if(null===(l=df(a))&&Wc(e,t,a,ff,n),l===r)break;r=l}null!==r&&a.stopPropagation()}else Wc(e,t,a,null,n)}}function df(e){return mf(e=Ft(e))}var ff=null;function mf(e){if(ff=null,null!==(e=qe(e))){var t=l(e);if(null===t)e=null;else{var n=t.tag;if(13===n){if(null!==(e=s(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return ff=e,null}function pf(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(se()){case ie:return 2;case oe:return 8;case ue:case ce:return 32;case de:return 268435456;default:return 32}default:return 32}}var hf=!1,gf=null,bf=null,vf=null,xf=new Map,yf=new Map,wf=[],Sf="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function kf(e,t){switch(e){case"focusin":case"focusout":gf=null;break;case"dragenter":case"dragleave":bf=null;break;case"mouseover":case"mouseout":vf=null;break;case"pointerover":case"pointerout":xf.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":yf.delete(t.pointerId)}}function Nf(e,t,n,a,r,l){return null===e||e.nativeEvent!==l?(e={blockedOn:t,domEventName:n,eventSystemFlags:a,nativeEvent:l,targetContainers:[r]},null!==t&&(null!==(t=We(t))&&lf(t)),e):(e.eventSystemFlags|=a,t=e.targetContainers,null!==r&&-1===t.indexOf(r)&&t.push(r),e)}function jf(e){var t=qe(e.target);if(null!==t){var n=l(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=s(n)))return e.blockedOn=t,void function(e,t){var n=I.p;try{return I.p=e,t()}finally{I.p=n}}(e.priority,(function(){if(13===n.tag){var e=Au();e=_e(e);var t=Fa(n,e);null!==t&&$u(t,0,e),rf(n,e)}}))}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Cf(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=df(e.nativeEvent);if(null!==n)return null!==(t=We(n))&&lf(t),e.blockedOn=n,!1;var a=new(n=e.nativeEvent).constructor(n.type,n);Ot=a,n.target.dispatchEvent(a),Ot=null,t.shift()}return!0}function Ef(e,t,n){Cf(e)&&n.delete(t)}function Tf(){hf=!1,null!==gf&&Cf(gf)&&(gf=null),null!==bf&&Cf(bf)&&(bf=null),null!==vf&&Cf(vf)&&(vf=null),xf.forEach(Ef),yf.forEach(Ef)}function Pf(t,n){t.blockedOn===n&&(t.blockedOn=null,hf||(hf=!0,e.unstable_scheduleCallback(e.unstable_NormalPriority,Tf)))}var zf=null;function Lf(t){zf!==t&&(zf=t,e.unstable_scheduleCallback(e.unstable_NormalPriority,(function(){zf===t&&(zf=null);for(var e=0;e<t.length;e+=3){var n=t[e],a=t[e+1],r=t[e+2];if("function"!=typeof a){if(null===mf(a||n))continue;break}var l=We(n);null!==l&&(t.splice(e,3),e-=3,Os(l,{pending:!0,data:r,method:n.method,action:a},a,r))}})))}function _f(e){function t(t){return Pf(t,e)}null!==gf&&Pf(gf,e),null!==bf&&Pf(bf,e),null!==vf&&Pf(vf,e),xf.forEach(t),yf.forEach(t);for(var n=0;n<wf.length;n++){var a=wf[n];a.blockedOn===e&&(a.blockedOn=null)}for(;0<wf.length&&null===(n=wf[0]).blockedOn;)jf(n),null===n.blockedOn&&wf.shift();if(null!=(n=(e.ownerDocument||e).$$reactFormReplay))for(a=0;a<n.length;a+=3){var r=n[a],l=n[a+1],s=r[De]||null;if("function"==typeof l)s||Lf(n);else if(s){var i=null;if(l&&l.hasAttribute("formAction")){if(r=l,s=l[De]||null)i=s.formAction;else if(null!==mf(r))continue}else i=s.action;"function"==typeof i?n[a+1]=i:(n.splice(a,3),a-=3),Lf(n)}}}function Mf(e){this._internalRoot=e}function Of(e){this._internalRoot=e}Of.prototype.render=Mf.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(a(409));nf(t.current,Au(),e,t,null,null)},Of.prototype.unmount=Mf.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;nf(e.current,2,null,e,null,null),qu(),t[Ae]=null}},Of.prototype.unstable_scheduleHydration=function(e){if(e){var t=Oe();e={blockedOn:null,target:e,priority:t};for(var n=0;n<wf.length&&0!==t&&t<wf[n].priority;n++);wf.splice(n,0,e),0===n&&jf(e)}};var Ff=t.version;if("19.1.0"!==Ff)throw Error(a(527,Ff,"19.1.0"));I.findDOMNode=function(e){var t=e._reactInternals;if(void 0===t){if("function"==typeof e.render)throw Error(a(188));throw e=Object.keys(e).join(","),Error(a(268,e))}return e=function(e){var t=e.alternate;if(!t){if(null===(t=l(e)))throw Error(a(188));return t!==e?null:e}for(var n=e,r=t;;){var s=n.return;if(null===s)break;var o=s.alternate;if(null===o){if(null!==(r=s.return)){n=r;continue}break}if(s.child===o.child){for(o=s.child;o;){if(o===n)return i(s),e;if(o===r)return i(s),t;o=o.sibling}throw Error(a(188))}if(n.return!==r.return)n=s,r=o;else{for(var u=!1,c=s.child;c;){if(c===n){u=!0,n=s,r=o;break}if(c===r){u=!0,r=s,n=o;break}c=c.sibling}if(!u){for(c=o.child;c;){if(c===n){u=!0,n=o,r=s;break}if(c===r){u=!0,r=o,n=s;break}c=c.sibling}if(!u)throw Error(a(189))}}if(n.alternate!==r)throw Error(a(190))}if(3!==n.tag)throw Error(a(188));return n.stateNode.current===n?e:t}(t),e=null===(e=null!==e?o(e):null)?null:e.stateNode};var Rf={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:A,reconcilerVersion:"19.1.0"};if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var Df=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Df.isDisabled&&Df.supportsFiber)try{pe=Df.inject(Rf),he=Df}catch(If){}}return b.createRoot=function(e,t){if(!r(e))throw Error(a(299));var n=!1,l="",s=wi,i=Si,o=ki;return null!=t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(l=t.identifierPrefix),void 0!==t.onUncaughtError&&(s=t.onUncaughtError),void 0!==t.onCaughtError&&(i=t.onCaughtError),void 0!==t.onRecoverableError&&(o=t.onRecoverableError),void 0!==t.unstable_transitionCallbacks&&t.unstable_transitionCallbacks),t=ef(e,1,!1,null,0,n,l,s,i,o,0,null),e[Ae]=t.current,Hc(e),new Mf(t)},b.hydrateRoot=function(e,t,n){if(!r(e))throw Error(a(299));var l=!1,s="",i=wi,o=Si,u=ki,c=null;return null!=n&&(!0===n.unstable_strictMode&&(l=!0),void 0!==n.identifierPrefix&&(s=n.identifierPrefix),void 0!==n.onUncaughtError&&(i=n.onUncaughtError),void 0!==n.onCaughtError&&(o=n.onCaughtError),void 0!==n.onRecoverableError&&(u=n.onRecoverableError),void 0!==n.unstable_transitionCallbacks&&n.unstable_transitionCallbacks,void 0!==n.formState&&(c=n.formState)),(t=ef(e,1,!0,t,0,l,s,i,o,u,0,c)).context=tf(null),n=t.current,(s=ol(l=_e(l=Au()))).callback=null,ul(n,s,l),n=l,t.current.lanes=n,Pe(t,n),Cc(t),e[Ae]=t.current,Hc(e),new Of(t)},b.version="19.1.0",b}const P=e((N||(N=1,function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){}}(),g.exports=T()),g.exports)),z="http://192.168.0.152",L="/settings",_=z;async function M(e){const t=e.headers.get("content-type");if(!e.ok){let n;try{if(t&&t.includes("application/json")){const t=await e.json();n=t.error||t.message||JSON.stringify(t)}else n=await e.text()}catch{n=`${e.status} ${e.statusText}`}throw new Error(`API Error: ${e.status} ${e.statusText} - ${n}`)}return t&&t.includes("application/json")?e.json():e.text()}async function O(){return M(await fetch(`${_}/status`))}async function F(e){return M(await fetch(`${_}/settings`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}))}async function R(e){return M(await fetch(`${_}/brightness`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}))}async function D(e=128){return async function(e=128){return M(await fetch(`${_}/calibrate/standard/white`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({brightness:e})}))}(e)}async function A(){return M(await fetch(`${_}/matrix-calibration/results`))}const I=({title:e,children:t,className:n,titleClassName:a})=>i.jsxs("div",{className:`bg-slate-800 shadow-md border border-slate-700 rounded-xl p-6 ${n||""}`,children:[e&&i.jsx("h2",{className:`text-xl font-semibold text-slate-200 mb-4 ${a||""}`,children:e}),t]}),$=({r:e,g:t,b:n,size:a="md",className:r,showRgbText:l=!1})=>{const s=`rgb(${e}, ${t}, ${n})`,o=.299*e+.587*t+.114*n>186?"#000000":"#FFFFFF";return i.jsx("div",{className:`rounded-md shadow-sm border border-slate-600 flex items-center justify-center ${{sm:"w-8 h-8",md:"w-12 h-12",lg:"w-20 h-20",xl:"w-32 h-32"}[a]} ${r||""}`,style:{backgroundColor:s},children:l&&i.jsxs("span",{style:{color:o},className:"text-xs font-mono",children:[e,",",t,",",n]})})},U=({size:e="md",className:t,message:n})=>i.jsxs("div",{className:`flex flex-col items-center justify-center ${t||""}`,children:[i.jsx("div",{className:`spinner ${{sm:"w-6 h-6 border-2",md:"w-8 h-8 border-4",lg:"w-12 h-12 border-4"}[e]} border-slate-600 border-t-blue-500 rounded-full animate-spin`}),n&&i.jsx("p",{className:"mt-2 text-sm text-slate-400",children:n})]}),V=({label:e,value:t})=>i.jsxs("div",{className:"flex justify-between py-1 border-b border-slate-700 last:border-b-0",children:[i.jsxs("span",{className:"text-sm text-slate-400",children:[e,":"]}),i.jsx("span",{className:"text-sm font-medium text-slate-100 text-right",children:t})]}),B=({status:e,isLoading:t,error:n,onRefresh:a})=>t&&!e?i.jsx(I,{title:"Device Status",children:i.jsx(U,{message:"Loading status..."})}):n?i.jsx(I,{title:"Device Status",children:i.jsxs("p",{className:"text-red-400",children:["Error: ",n]})}):e?i.jsxs(I,{title:"Device Status",className:"mb-6",children:[i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-2",children:[i.jsx(V,{label:"Scanning",value:e.isScanning?"Yes":"No"}),i.jsx(V,{label:"LED On",value:e.ledState?"Yes":"No"}),i.jsx(V,{label:"Calibrated",value:e.isCalibrated?"Yes":"No"}),i.jsx(V,{label:"Sample Count",value:e.sampleCount}),i.jsx(V,{label:"ATIME",value:e.atime}),i.jsx(V,{label:"AGAIN",value:e.again}),i.jsx(V,{label:"Scan Brightness",value:e.brightness}),i.jsx(V,{label:"Ambient Lux",value:`${e.ambientLux.toFixed(2)} lx`}),e.rssi&&i.jsx(V,{label:"WiFi RSSI",value:`${e.rssi} dBm`}),e.esp32IP&&i.jsx(V,{label:"Device IP",value:e.esp32IP}),i.jsxs("div",{className:"md:col-span-2 flex items-center space-x-2 py-1",children:[i.jsx("span",{className:"text-sm text-slate-400",children:"Current LED Color:"}),i.jsx($,{r:e.currentR,g:e.currentG,b:e.currentB,size:"sm"})]})]}),i.jsx("div",{className:"mt-4 flex justify-end",children:i.jsx("button",{onClick:a,disabled:t,className:"text-sm text-blue-400 hover:text-blue-300 disabled:opacity-50",children:t?"Refreshing...":"Refresh Status"})})]}):i.jsx(I,{title:"Device Status",children:i.jsx("p",{className:"text-slate-400",children:"No status data available."})}),H=({children:e,className:t,variant:n="primary",size:a="md",isLoading:r=!1,disabled:l,...s})=>i.jsxs("button",{type:"button",className:`font-semibold rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-150 ease-in-out flex items-center justify-center ${{primary:"bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500",secondary:"bg-slate-600 hover:bg-slate-700 text-slate-100 focus:ring-slate-500 border border-slate-500",danger:"bg-red-600 hover:bg-red-700 text-white focus:ring-red-500",success:"bg-green-600 hover:bg-green-700 text-white focus:ring-green-500"}[n]} ${{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-base",lg:"px-6 py-3 text-lg"}[a]} disabled:opacity-50 disabled:cursor-not-allowed ${t||""}`,disabled:l||r,...s,children:[r&&i.jsxs("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[i.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),i.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),e]}),q=({onSampleSaved:e,showToast:t})=>{const[n,a]=f.useState(null),[r,l]=f.useState(!1),[s,o]=f.useState(!1);return i.jsx(I,{title:"Color Scanner",className:"mb-6",children:i.jsxs("div",{className:"flex flex-col items-center space-y-4",children:[i.jsx(H,{onClick:async()=>{l(!0),a(null);try{const e=await async function(){return M(await fetch(`${_}/scan`,{method:"POST"}))}();a(e),t("Scan successful!","success")}catch(e){t(e instanceof Error?e.message:"Scan failed.","error")}finally{l(!1)}},isLoading:r,disabled:r||s,variant:"primary",size:"lg",children:"Scan Color"}),r&&i.jsx(U,{message:"Scanning..."}),n&&i.jsxs("div",{className:"mt-6 p-4 border border-slate-700 rounded-lg shadow-sm w-full max-w-md text-center",children:[i.jsx("h3",{className:"text-lg font-medium text-slate-200 mb-3",children:"Scanned Color:"}),i.jsx("div",{className:"flex justify-center mb-3",children:i.jsx($,{r:n.r,g:n.g,b:n.b,size:"xl"})}),i.jsxs("p",{className:"font-mono text-slate-300",children:["RGB: (",n.r,", ",n.g,", ",n.b,")"]}),i.jsxs("p",{className:"font-mono text-xs text-slate-400",children:["XYZ: (",n.x,", ",n.y,", ",n.z,") IR: ",n.ir]}),i.jsx(H,{onClick:async()=>{if(n){o(!0);try{await async function(e,t,n){return M(await fetch(`${_}/save`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({r:e,g:t,b:n})}))}(n.r,n.g,n.b),t("Sample saved!","success"),e(),a(null)}catch(r){t(r instanceof Error?r.message:"Save sample failed.","error")}finally{o(!1)}}else t("No color scanned to save.","error")},isLoading:s,disabled:r||s,variant:"success",className:"mt-4",children:"Save Sample"})]})]})})},W=["Dark/Low-light","Indoor","Bright","Very Bright"],Q=["1x","4x","16x","64x"],Z=()=>{const[e,t]=f.useState(!1),[n,a]=f.useState(null),[r,l]=f.useState(null),[s,o]=f.useState(null),[u,c]=f.useState(!1),d=e=>new Date(e).toLocaleTimeString();return i.jsx("div",{className:"space-y-6",children:i.jsxs(I,{className:"p-6",children:[i.jsx("h2",{className:"text-xl font-semibold text-slate-100 mb-4",children:"Enhanced Color Scanning"}),i.jsx("p",{className:"text-slate-300 text-sm mb-4",children:"Uses dynamic sensor optimization for accurate color matching across all lighting conditions."}),i.jsxs("div",{className:"flex gap-3 mb-4",children:[i.jsx(H,{onClick:async()=>{t(!0),o(null);try{const e=await fetch(`${z}/enhanced-scan`,{method:"POST",headers:{"Content-Type":"application/json"}});if(!e.ok)throw new Error(`HTTP error! status: ${e.status}`);const t=await e.json();a(t)}catch(e){o(e instanceof Error?e.message:"Enhanced scan failed")}finally{t(!1)}},disabled:e,className:"flex-1",children:e?i.jsxs(i.Fragment,{children:[i.jsx(U,{size:"sm"}),i.jsx("span",{className:"ml-2",children:"Scanning..."})]}):"Enhanced Scan"}),i.jsx(H,{onClick:async()=>{try{const e=await fetch(`${z}/sensor-diagnostics`);if(!e.ok)throw new Error(`HTTP error! status: ${e.status}`);const t=await e.json();l(t)}catch(e){o(e instanceof Error?e.message:"Failed to fetch diagnostics")}},variant:"secondary",className:"flex-1",children:"Sensor Diagnostics"})]}),s&&i.jsx("div",{className:"bg-red-900/50 border border-red-500 rounded-lg p-3 mb-4",children:i.jsx("p",{className:"text-red-200 text-sm",children:s})}),n&&i.jsxs("div",{className:"bg-slate-800 rounded-lg p-4 mb-4",children:[i.jsx("h3",{className:"text-lg font-medium text-slate-100 mb-3",children:"Scan Results"}),i.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[i.jsxs("div",{children:[i.jsx("h4",{className:"text-sm font-medium text-slate-300 mb-2",children:"Color Values"}),i.jsxs("div",{className:"space-y-1 text-sm",children:[i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-slate-400",children:"RGB:"}),i.jsxs("span",{className:"text-slate-200",children:["(",n.r,", ",n.g,", ",n.b,")"]})]}),i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-slate-400",children:"XYZ:"}),i.jsxs("span",{className:"text-slate-200",children:["(",n.x,", ",n.y,", ",n.z,")"]})]}),i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-slate-400",children:"IR:"}),i.jsxs("span",{className:"text-slate-200",children:["(",n.ir1,", ",n.ir2,")"]})]})]})]}),i.jsxs("div",{children:[i.jsx("h4",{className:"text-sm font-medium text-slate-300 mb-2",children:"Color Preview"}),i.jsx("div",{className:"w-full h-16 rounded border border-slate-600",style:{backgroundColor:`rgb(${n.r}, ${n.g}, ${n.b})`}})]})]}),n.sensorConfig&&i.jsxs("div",{className:"border-t border-slate-600 pt-3",children:[i.jsx("h4",{className:"text-sm font-medium text-slate-300 mb-2",children:"Sensor Configuration"}),i.jsxs("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-slate-400",children:"Integration Time:"}),i.jsxs("span",{className:"text-slate-200",children:[n.sensorConfig.atime,"ms"]})]}),i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-slate-400",children:"Gain:"}),i.jsx("span",{className:"text-slate-200",children:Q[n.sensorConfig.again]})]}),i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-slate-400",children:"Brightness:"}),i.jsx("span",{className:"text-slate-200",children:n.sensorConfig.brightness})]}),i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-slate-400",children:"Condition:"}),i.jsx("span",{className:"text-slate-200",children:W[n.sensorConfig.condition]})]}),i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-slate-400",children:"Optimal:"}),i.jsx("span",{className:n.sensorConfig.isOptimal?"text-green-400":"text-yellow-400",children:n.sensorConfig.isOptimal?"Yes":"No"})]})]})]}),n.brightnessOptimization&&i.jsxs("div",{className:"border-t border-slate-600 pt-3 mt-3",children:[i.jsx("h4",{className:"text-sm font-medium text-slate-300 mb-2",children:"Automatic Brightness Optimization"}),i.jsxs("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-slate-400",children:"Control Variable:"}),i.jsx("span",{className:"text-slate-200",children:n.brightnessOptimization.controlVariable})]}),i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-slate-400",children:"Target Range:"}),i.jsxs("span",{className:"text-slate-200",children:[n.brightnessOptimization.targetMin," - ",n.brightnessOptimization.targetMax]})]}),i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-slate-400",children:"In Optimal Range:"}),i.jsx("span",{className:n.brightnessOptimization.inOptimalRange?"text-green-400":"text-yellow-400",children:n.brightnessOptimization.inOptimalRange?"Yes":"No"})]}),i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-slate-400",children:"Optimized Brightness:"}),i.jsx("span",{className:"text-slate-200",children:n.brightnessOptimization.optimizedBrightness})]})]})]}),i.jsxs("div",{className:"text-xs text-slate-500 mt-3",children:["Scanned at ",d(n.timestamp)]})]}),r&&i.jsxs("div",{className:"bg-slate-800 rounded-lg p-4",children:[i.jsxs("div",{className:"flex justify-between items-center mb-3",children:[i.jsx("h3",{className:"text-lg font-medium text-slate-100",children:"Sensor Diagnostics"}),i.jsx(H,{onClick:()=>c(!u),variant:"secondary",size:"sm",children:u?"Hide Details":"Show Details"})]}),i.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-3",children:[i.jsxs("div",{children:[i.jsx("h4",{className:"text-sm font-medium text-slate-300 mb-2",children:"Current Readings"}),i.jsxs("div",{className:"space-y-1 text-sm",children:[i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-slate-400",children:"XYZ:"}),i.jsxs("span",{className:"text-slate-200",children:["(",r.currentReadings.x,", ",r.currentReadings.y,", ",r.currentReadings.z,")"]})]}),i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-slate-400",children:"Saturated:"}),i.jsx("span",{className:r.currentReadings.saturated?"text-red-400":"text-green-400",children:r.currentReadings.saturated?"Yes":"No"})]})]})]}),i.jsxs("div",{children:[i.jsx("h4",{className:"text-sm font-medium text-slate-300 mb-2",children:"Dynamic Sensor"}),i.jsxs("div",{className:"space-y-1 text-sm",children:[i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-slate-400",children:"Enabled:"}),i.jsx("span",{className:r.dynamicSensor.enabled?"text-green-400":"text-red-400",children:r.dynamicSensor.enabled?"Yes":"No"})]}),i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-slate-400",children:"Initialized:"}),i.jsx("span",{className:r.dynamicSensor.initialized?"text-green-400":"text-red-400",children:r.dynamicSensor.initialized?"Yes":"No"})]}),void 0!==r.dynamicSensor.detectedCondition&&i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-slate-400",children:"Condition:"}),i.jsx("span",{className:"text-slate-200",children:W[r.dynamicSensor.detectedCondition]})]})]})]})]}),u&&i.jsx("div",{className:"border-t border-slate-600 pt-3",children:i.jsx("pre",{className:"text-xs text-slate-300 bg-slate-900 p-3 rounded overflow-auto max-h-64",children:JSON.stringify(r,null,2)})}),i.jsxs("div",{className:"text-xs text-slate-500 mt-3",children:["Updated at ",d(r.timestamp)]})]})]})})},G=({sample:e,index:t,onDelete:n})=>{const[a,r]=f.useState(!1);return i.jsx("li",{className:"bg-slate-700 p-4 rounded-lg shadow hover:shadow-md transition-shadow duration-150 relative group",children:i.jsxs("div",{className:"flex items-start space-x-4",children:[i.jsx($,{r:e.r,g:e.g,b:e.b,size:"lg"}),i.jsxs("div",{className:"flex-1",children:[i.jsx("h3",{className:"text-sm font-semibold text-slate-100",children:e.paintName&&"Unknown"!==e.paintName?e.paintName:`RGB: ${e.r}, ${e.g}, ${e.b}`}),e.paintCode&&"N/A"!==e.paintCode&&i.jsxs("p",{className:"text-xs text-slate-300",children:["Code: ",e.paintCode]}),e.paintName&&"Unknown"!==e.paintName&&i.jsxs("p",{className:"text-xs text-slate-400 font-mono",children:["RGB: ",e.r,", ",e.g,", ",e.b]}),e.lrv>0&&i.jsxs("p",{className:"text-xs text-slate-400",children:["LRV: ",e.lrv.toFixed(1)]}),i.jsxs("p",{className:"text-xs text-slate-500 mt-1",children:["Saved: ",new Date(e.timestamp).toLocaleString()]})]}),i.jsx("button",{onClick:async e=>{if(e.preventDefault(),e.stopPropagation(),window.confirm("Are you sure you want to delete this sample?")){r(!0);try{await n(t)}finally{r(!1)}}},disabled:a,className:"absolute top-2 right-2 w-6 h-6 bg-red-600 hover:bg-red-700 disabled:bg-red-800 disabled:opacity-50 text-white rounded-full flex items-center justify-center text-xs font-bold transition-colors duration-150",title:"Delete sample",children:a?i.jsx("div",{className:"w-3 h-3 border border-white border-t-transparent rounded-full animate-spin"}):"×"})]})})},Y=({samples:e,isLoading:t,error:n,onSampleDeleted:a,showToast:r})=>{const[l,s]=f.useState(!1),[o,u]=f.useState(!1),c=async e=>{try{const t=await async function(e){return M(await fetch(`${_}/delete`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({index:e})}))}(e);t.success?(r("Sample deleted successfully!","success"),a()):r(t.message||"Failed to delete sample","error")}catch(t){r(t instanceof Error?t.message:"Failed to delete sample","error")}};return t?i.jsx(I,{title:"Saved Samples",children:i.jsx(U,{message:"Loading samples..."})}):n?i.jsx(I,{title:"Saved Samples",children:i.jsxs("p",{className:"text-red-400",children:["Error: ",n]})}):i.jsx(I,{title:"Saved Samples",className:"mb-6",children:0===e.length?i.jsx("p",{className:"text-slate-400",children:"No samples saved yet."}):i.jsxs(i.Fragment,{children:[i.jsx("div",{className:"mb-4 flex justify-end",children:i.jsx("button",{onClick:()=>u(!0),disabled:l,className:"px-3 py-1 bg-red-600 hover:bg-red-700 disabled:bg-red-800 disabled:opacity-50 text-white text-sm rounded transition-colors duration-150",title:"Delete all samples",children:l?i.jsxs("div",{className:"flex items-center",children:[i.jsx("div",{className:"w-3 h-3 border border-white border-t-transparent rounded-full animate-spin mr-2"}),"Deleting..."]}):"Delete All"})}),i.jsx("ul",{className:"space-y-3 max-h-96 overflow-y-auto pr-2",children:e.map(((e,t)=>i.jsx(G,{sample:e,index:t,onDelete:c},`${e.timestamp}-${t}`)))}),o&&i.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:i.jsxs("div",{className:"bg-slate-800 p-6 rounded-lg shadow-xl max-w-md w-full mx-4",children:[i.jsx("h3",{className:"text-lg font-semibold text-slate-100 mb-4",children:"Confirm Delete All"}),i.jsxs("p",{className:"text-slate-300 mb-6",children:["Are you sure you want to delete all ",e.length," saved samples? This action cannot be undone."]}),i.jsxs("div",{className:"flex justify-end space-x-3",children:[i.jsx("button",{onClick:()=>u(!1),disabled:l,className:"px-4 py-2 bg-slate-600 hover:bg-slate-700 disabled:opacity-50 text-white rounded transition-colors duration-150",children:"Cancel"}),i.jsx("button",{onClick:async()=>{s(!0);try{const e=await async function(){return M(await fetch(`${_}/samples/clear`,{method:"POST",headers:{"Content-Type":"application/json"}}))}();e.success?(r("All samples deleted successfully!","success"),a()):r(e.message||"Failed to delete all samples","error")}catch(e){r(e instanceof Error?e.message:"Failed to delete all samples","error")}finally{s(!1),u(!1)}},disabled:l,className:"px-4 py-2 bg-red-600 hover:bg-red-700 disabled:bg-red-800 disabled:opacity-50 text-white rounded transition-colors duration-150",children:l?"Deleting...":"Delete All"})]})]})})]})})},K=({label:e,id:t,wrapperClassName:n,className:a,...r})=>i.jsxs("div",{className:n,children:[e&&i.jsx("label",{htmlFor:t,className:"block text-sm font-medium text-slate-300 mb-1",children:e}),i.jsx("input",{id:t,className:`block w-full px-3 py-2 border border-slate-600 rounded-md bg-slate-700 text-slate-100 placeholder-slate-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${a||""}`,...r})]}),X=({id:e,label:t,min:n,max:a,step:r=1,value:l,onChange:s,disabled:o,unit:u})=>i.jsxs("div",{children:[i.jsxs("label",{htmlFor:e,className:"block text-sm font-medium text-slate-300",children:[t,": ",i.jsxs("span",{className:"font-semibold text-slate-100",children:[l,u]})]}),i.jsx("input",{type:"range",id:e,min:n,max:a,step:r,value:l,onChange:e=>s(parseInt(e.target.value,10)),disabled:o,className:"w-full h-2 bg-slate-600 rounded-lg appearance-none cursor-pointer accent-blue-500 disabled:opacity-50 disabled:cursor-not-allowed mt-1"})]}),J=({id:e,label:t,checked:n,onChange:a,disabled:r})=>i.jsxs("div",{className:"flex items-center",children:[i.jsx("button",{type:"button",id:e,role:"switch","aria-checked":n,onClick:()=>!r&&a(!n),className:`${n?"bg-blue-600":"bg-slate-600"} relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-900 ${r?"opacity-50 cursor-not-allowed":""}`,disabled:r,children:i.jsx("span",{"aria-hidden":"true",className:(n?"translate-x-5":"translate-x-0")+" pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"})}),i.jsx("label",{htmlFor:e,className:"ml-3 text-sm font-medium text-slate-300 cursor-pointer",children:t})]}),ee=({showToast:e})=>{var t,n,a,r,l,s;const[o,u]=f.useState(null),[c,d]=f.useState("idle"),[m,p]=f.useState([]),[h,g]=f.useState(0),[b,v]=f.useState(!1),[x,y]=f.useState(null),[w,S]=f.useState(null);f.useEffect((()=>{k()}),[]);const k=async()=>{try{const e=await async function(){return M(await fetch(`${_}/matrix-calibration/status`))}();if(u(e),e.matrixValid){const e=await A();y(e)}}catch(t){e("Failed to load calibration status","error")}},N=async()=>{v(!0);try{const t=await async function(){return M(await fetch(`${_}/matrix-calibration/compute`,{method:"POST",headers:{"Content-Type":"application/json"}}))}();if(t.success){S(t),d("results"),e("Calibration matrix computed successfully","success");const n=await A();y(n)}else e(`Failed to compute matrix: ${t.error}`,"error")}catch(t){e("Failed to compute calibration matrix","error")}finally{v(!1)}},j=e=>e>=90?"text-green-400":e>=70?"text-yellow-400":e>=50?"text-orange-400":"text-red-400";return i.jsx(I,{className:"bg-slate-800 border-slate-600",children:i.jsxs("div",{className:"space-y-6",children:[i.jsxs("div",{className:"flex items-center justify-between",children:[i.jsx("h3",{className:"text-lg font-semibold text-slate-100",children:"Matrix Calibration"}),(null==o?void 0:o.matrixValid)&&i.jsxs("div",{className:"flex items-center space-x-2",children:[i.jsx("div",{className:"w-2 h-2 bg-green-400 rounded-full"}),i.jsx("span",{className:"text-sm text-green-400",children:"Active"})]})]}),o&&i.jsx("div",{className:"bg-slate-700 p-3 rounded-lg border border-slate-600",children:i.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[i.jsxs("div",{children:[i.jsx("span",{className:"text-slate-400",children:"Status:"}),i.jsx("span",{className:"ml-2 "+(o.matrixValid?"text-green-400":"text-slate-300"),children:o.matrixValid?"Calibrated":"Not Calibrated"})]}),i.jsxs("div",{children:[i.jsx("span",{className:"text-slate-400",children:"Points:"}),i.jsx("span",{className:"ml-2 text-slate-300",children:o.numPoints})]}),o.matrixValid&&i.jsxs(i.Fragment,{children:[i.jsxs("div",{children:[i.jsx("span",{className:"text-slate-400",children:"Avg ΔE:"}),i.jsx("span",{className:"ml-2 text-slate-300",children:null==(t=o.avgDeltaE)?void 0:t.toFixed(2)})]}),i.jsxs("div",{children:[i.jsx("span",{className:"text-slate-400",children:"Quality:"}),i.jsx("span",{className:`ml-2 ${j(o.qualityScore||0)}`,children:(C=o.qualityScore||0,C>=90?"Excellent":C>=70?"Good":C>=50?"Fair":"Poor")})]})]})]})}),"idle"===c&&i.jsxs("div",{className:"space-y-4",children:[i.jsxs("div",{className:"bg-blue-900/30 p-3 rounded-lg border border-blue-600",children:[i.jsx("p",{className:"text-sm text-blue-300 mb-3",children:"Matrix calibration provides superior color accuracy across the full spectrum by using multiple color references."}),i.jsx("p",{className:"text-xs text-slate-400",children:"This process will guide you through measuring 7 standard colors to compute a transformation matrix."})]}),i.jsxs("div",{className:"flex space-x-3",children:[i.jsx(H,{onClick:async()=>{v(!0);try{const t=await async function(){return M(await fetch(`${_}/matrix-calibration/start`,{method:"POST",headers:{"Content-Type":"application/json"}}))}();t.success?(p(t.colorReferences),g(0),d("measuring"),e("Matrix calibration started","success")):e("Failed to start calibration","error")}catch(t){e("Failed to start calibration","error")}finally{v(!1)}},variant:"primary",isLoading:b,className:"flex-1",children:"Start Matrix Calibration"}),(null==o?void 0:o.matrixValid)&&i.jsx(H,{onClick:async()=>{v(!0);try{(await async function(){return M(await fetch(`${_}/matrix-calibration/clear`,{method:"DELETE",headers:{"Content-Type":"application/json"}}))}()).success?(e("Matrix calibration cleared","success"),d("idle"),p([]),y(null),S(null),await k()):e("Failed to clear calibration","error")}catch(t){e("Failed to clear calibration","error")}finally{v(!1)}},variant:"secondary",isLoading:b,children:"Clear"})]})]}),"measuring"===c&&i.jsxs("div",{className:"space-y-4",children:[i.jsxs("div",{className:"bg-amber-900/30 p-3 rounded-lg border border-amber-600",children:[i.jsxs("h4",{className:"text-amber-300 font-medium mb-2",children:["Step ",h+1," of ",m.length]}),h<m.length&&i.jsxs("div",{className:"flex items-center space-x-3",children:[i.jsx("div",{className:"w-8 h-8 rounded border border-slate-500",style:{backgroundColor:`rgb(${m[h].r}, ${m[h].g}, ${m[h].b})`}}),i.jsxs("div",{children:[i.jsx("p",{className:"text-amber-300 font-medium",children:m[h].name}),i.jsxs("p",{className:"text-xs text-slate-400",children:["Place sensor over ",m[h].name.toLowerCase()," color patch"]})]})]})]}),i.jsxs("div",{className:"space-y-2",children:[i.jsxs("div",{className:"flex justify-between text-sm",children:[i.jsx("span",{className:"text-slate-400",children:"Progress"}),i.jsxs("span",{className:"text-slate-300",children:[m.filter((e=>e.measured)).length," / ",m.length]})]}),i.jsx("div",{className:"w-full bg-slate-700 rounded-full h-2",children:i.jsx("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-300",style:{width:m.filter((e=>e.measured)).length/m.length*100+"%"}})})]}),i.jsx("div",{className:"grid grid-cols-4 gap-2",children:m.map(((e,t)=>i.jsxs("div",{className:"p-2 rounded border text-center text-xs "+(t===h?"border-amber-500 bg-amber-900/20":e.measured?"border-green-500 bg-green-900/20":"border-slate-600 bg-slate-700"),children:[i.jsx("div",{className:"w-full h-6 rounded mb-1 border border-slate-500",style:{backgroundColor:`rgb(${e.r}, ${e.g}, ${e.b})`}}),i.jsx("div",{className:""+(t===h?"text-amber-300":e.measured?"text-green-400":"text-slate-400"),children:e.name}),e.measured&&i.jsx("div",{className:"text-green-400 text-xs",children:"✓"})]},e.name)))}),i.jsxs("div",{className:"flex space-x-3",children:[h<m.length?i.jsxs(H,{onClick:async()=>{if(h>=m.length)return;const t=m[h];v(!0);try{const n=await async function(e){return M(await fetch(`${_}/matrix-calibration/measure`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}))}({colorName:t.name,r:t.r,g:t.g,b:t.b});if(n.success){const n=[...m];n[h].measured=!0,p(n),e(`${t.name} measured successfully`,"success"),h<m.length-1?g(h+1):d("computing")}else e(`Failed to measure ${t.name}: ${n.error}`,"error")}catch(n){e(`Failed to measure ${t.name}`,"error")}finally{v(!1)}},variant:"primary",isLoading:b,className:"flex-1",children:["Measure ",null==(n=m[h])?void 0:n.name]}):i.jsx(H,{onClick:N,variant:"primary",isLoading:b,className:"flex-1",children:"Compute Matrix"}),i.jsx(H,{onClick:()=>d("idle"),variant:"secondary",children:"Cancel"})]})]}),"computing"===c&&i.jsxs("div",{className:"space-y-4",children:[i.jsxs("div",{className:"bg-blue-900/30 p-3 rounded-lg border border-blue-600",children:[i.jsx("h4",{className:"text-blue-300 font-medium mb-2",children:"Computing Calibration Matrix"}),i.jsx("p",{className:"text-sm text-blue-300",children:"All colors measured successfully. Ready to compute transformation matrix."})]}),i.jsxs("div",{className:"flex space-x-3",children:[i.jsx(H,{onClick:N,variant:"primary",isLoading:b,className:"flex-1",children:"Compute Matrix"}),i.jsx(H,{onClick:()=>d("idle"),variant:"secondary",children:"Cancel"})]})]}),"results"===c&&w&&i.jsxs("div",{className:"space-y-4",children:[i.jsxs("div",{className:"bg-green-900/30 p-3 rounded-lg border border-green-600",children:[i.jsx("h4",{className:"text-green-300 font-medium mb-2",children:"Calibration Complete"}),i.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[i.jsxs("div",{children:[i.jsx("span",{className:"text-slate-400",children:"Quality Score:"}),i.jsxs("span",{className:`ml-2 font-medium ${j(w.qualityScore||0)}`,children:[null==(a=w.qualityScore)?void 0:a.toFixed(1),"% (",w.quality,")"]})]}),i.jsxs("div",{children:[i.jsx("span",{className:"text-slate-400",children:"Average ΔE:"}),i.jsx("span",{className:"ml-2 text-slate-300",children:null==(r=w.avgDeltaE)?void 0:r.toFixed(2)})]}),i.jsxs("div",{children:[i.jsx("span",{className:"text-slate-400",children:"Points ΔE < 2:"}),i.jsx("span",{className:"ml-2 text-green-400",children:w.pointsUnder2})]}),i.jsxs("div",{children:[i.jsx("span",{className:"text-slate-400",children:"Points ΔE < 5:"}),i.jsx("span",{className:"ml-2 text-yellow-400",children:w.pointsUnder5})]})]})]}),x&&x.calibrationPoints&&i.jsxs("div",{className:"bg-slate-700 p-3 rounded-lg border border-slate-600",children:[i.jsx("h5",{className:"text-slate-300 font-medium mb-3",children:"Calibration Points"}),i.jsx("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:x.calibrationPoints.map(((e,t)=>i.jsxs("div",{className:"flex items-center justify-between text-sm",children:[i.jsxs("div",{className:"flex items-center space-x-2",children:[i.jsx("div",{className:"w-4 h-4 rounded border border-slate-500",style:{backgroundColor:`rgb(${e.refR}, ${e.refG}, ${e.refB})`}}),i.jsx("span",{className:"text-slate-300",children:e.name})]}),i.jsxs("div",{className:"flex items-center space-x-4",children:[i.jsxs("span",{className:"text-slate-400 text-xs",children:["Sensor: ",e.sensorR,", ",e.sensorG,", ",e.sensorB]}),i.jsxs("span",{className:"font-medium "+(e.deltaE<2?"text-green-400":e.deltaE<5?"text-yellow-400":"text-red-400"),children:["ΔE ",e.deltaE.toFixed(2)]})]})]},t)))})]}),i.jsxs("div",{className:"flex space-x-3",children:[i.jsx(H,{onClick:async()=>{v(!0);try{const t=await async function(){return M(await fetch(`${_}/matrix-calibration/apply`,{method:"POST",headers:{"Content-Type":"application/json"}}))}();t.success?(e("Matrix calibration applied successfully","success"),d("idle"),await k()):e(`Failed to apply calibration: ${t.error}`,"error")}catch(t){e("Failed to apply calibration","error")}finally{v(!1)}},variant:"primary",isLoading:b,className:"flex-1",children:"Apply Calibration"}),i.jsx(H,{onClick:()=>d("idle"),variant:"secondary",children:"Discard"})]})]}),"idle"===c&&(null==o?void 0:o.matrixValid)&&x&&i.jsxs("div",{className:"space-y-4",children:[i.jsx("h4",{className:"text-slate-300 font-medium",children:"Current Matrix Calibration"}),i.jsxs("div",{className:"bg-slate-700 p-3 rounded-lg border border-slate-600",children:[i.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm mb-3",children:[i.jsxs("div",{children:[i.jsx("span",{className:"text-slate-400",children:"Quality Score:"}),i.jsxs("span",{className:`ml-2 font-medium ${j(x.qualityScore||0)}`,children:[null==(l=x.qualityScore)?void 0:l.toFixed(1),"%"]})]}),i.jsxs("div",{children:[i.jsx("span",{className:"text-slate-400",children:"Average ΔE:"}),i.jsx("span",{className:"ml-2 text-slate-300",children:null==(s=x.avgDeltaE)?void 0:s.toFixed(2)})]}),i.jsxs("div",{children:[i.jsx("span",{className:"text-slate-400",children:"Excellent (ΔE < 2):"}),i.jsxs("span",{className:"ml-2 text-green-400",children:[x.pointsUnder2,"/",x.numPoints]})]}),i.jsxs("div",{children:[i.jsx("span",{className:"text-slate-400",children:"Acceptable (ΔE < 5):"}),i.jsxs("span",{className:"ml-2 text-yellow-400",children:[x.pointsUnder5,"/",x.numPoints]})]})]}),x.timestamp&&i.jsxs("div",{className:"text-xs text-slate-400",children:["Calibrated: ",new Date(x.timestamp).toLocaleString()]})]})]})]})});var C};function te({isActive:e=!0,updateInterval:t=1500}){const[n,a]=f.useState(null),[r,l]=f.useState(!1),[s,o]=f.useState(null),u=f.useRef(null);f.useEffect((()=>(e?c():d(),()=>d())),[e,t]);const c=()=>{d(),m(),u.current=setInterval(m,t)},d=()=>{u.current&&(clearInterval(u.current),u.current=null)},m=async()=>{try{const e=await fetch(`${z}/live-metrics`,{method:"GET",headers:{Accept:"application/json"}});if(!e.ok)throw new Error(`HTTP ${e.status}: ${e.statusText}`);{const t=e.headers.get("content-type");if(!t||!t.includes("application/json"))throw new Error("Live metrics endpoint not available - firmware update needed");{const t=await e.json();a(t),l(!0),o(null)}}}catch(e){l(!1),o(e instanceof Error?e.message:"Unknown error")}},p=e=>{switch(e){case"optimal":case"normal":case"clean":case"adequate":return"text-green-400 bg-green-900/30";case"high":case"low":case"saturated":case"contaminated":return"text-red-400 bg-red-900/30";default:return"text-yellow-400 bg-yellow-900/30"}};return e?i.jsxs("div",{className:"bg-slate-800 rounded-lg p-6 border border-slate-600",children:[i.jsxs("div",{className:"flex items-center justify-between mb-4",children:[i.jsx("h3",{className:"text-lg font-semibold text-slate-200",children:"Live Sensor Metrics"}),i.jsxs("div",{className:"flex items-center space-x-2",children:[i.jsx("div",{className:"w-2 h-2 rounded-full "+(r?"bg-green-400":"bg-red-400")}),i.jsx("span",{className:"text-xs text-slate-400",children:r?"Connected":"Disconnected"})]})]}),s&&i.jsxs("div",{className:"mb-4 p-3 rounded-md text-sm bg-red-900/30 text-red-400 border border-red-700",children:["Connection Error: ",s]}),n?i.jsxs("div",{className:"space-y-4",children:[i.jsxs("div",{children:[i.jsx("h4",{className:"text-sm font-medium text-slate-300 mb-2",children:"Current Sensor Readings"}),i.jsxs("div",{className:"grid grid-cols-2 gap-3 text-sm",children:[i.jsxs("div",{className:"bg-slate-700/50 rounded p-2",children:[i.jsx("span",{className:"text-slate-400",children:"X (Red):"}),i.jsx("span",{className:"text-slate-200 ml-2 font-mono",children:n.sensorReadings.x})]}),i.jsxs("div",{className:"bg-slate-700/50 rounded p-2",children:[i.jsx("span",{className:"text-slate-400",children:"Y (Green):"}),i.jsx("span",{className:"text-slate-200 ml-2 font-mono",children:n.sensorReadings.y})]}),i.jsxs("div",{className:"bg-slate-700/50 rounded p-2",children:[i.jsx("span",{className:"text-slate-400",children:"Z (Blue):"}),i.jsx("span",{className:"text-slate-200 ml-2 font-mono",children:n.sensorReadings.z})]}),i.jsxs("div",{className:"bg-slate-700/50 rounded p-2",children:[i.jsx("span",{className:"text-slate-400",children:"IR:"}),i.jsx("span",{className:"text-slate-200 ml-2 font-mono",children:n.sensorReadings.ir})]})]})]}),i.jsxs("div",{children:[i.jsx("h4",{className:"text-sm font-medium text-slate-300 mb-2",children:"Control Metrics"}),i.jsxs("div",{className:"space-y-2",children:[i.jsxs("div",{className:"flex justify-between items-center",children:[i.jsx("span",{className:"text-slate-400",children:"Control Variable:"}),i.jsxs("div",{className:"flex items-center space-x-2",children:[i.jsx("span",{className:"text-slate-200 font-mono",children:n.metrics.controlVariable}),i.jsx("span",{className:`px-2 py-1 rounded text-xs ${p(n.statusIndicators.controlVariableStatus)}`,children:n.statusIndicators.controlVariableStatus})]})]}),i.jsxs("div",{className:"flex justify-between items-center",children:[i.jsx("span",{className:"text-slate-400",children:"Target Range:"}),i.jsxs("span",{className:"text-slate-200 font-mono",children:[n.metrics.targetMin," - ",n.metrics.targetMax]})]}),i.jsxs("div",{className:"flex justify-between items-center",children:[i.jsx("span",{className:"text-slate-400",children:"IR Ratio:"}),i.jsxs("div",{className:"flex items-center space-x-2",children:[i.jsx("span",{className:"text-slate-200 font-mono",children:((e,t=0)=>e.toFixed(t))(n.metrics.irRatio,3)}),i.jsx("span",{className:`px-2 py-1 rounded text-xs ${p(n.statusIndicators.irContaminationStatus)}`,children:n.statusIndicators.irContaminationStatus})]})]})]})]}),i.jsxs("div",{children:[i.jsx("h4",{className:"text-sm font-medium text-slate-300 mb-2",children:"LED Status"}),i.jsxs("div",{className:"space-y-2",children:[i.jsxs("div",{className:"flex justify-between items-center",children:[i.jsx("span",{className:"text-slate-400",children:"Current Brightness:"}),i.jsx("span",{className:"text-slate-200 font-mono",children:n.ledStatus.currentBrightness})]}),i.jsxs("div",{className:"flex justify-between items-center",children:[i.jsx("span",{className:"text-slate-400",children:"Enhanced Mode:"}),i.jsx("span",{className:"px-2 py-1 rounded text-xs "+(n.ledStatus.enhancedMode?"text-green-400 bg-green-900/30":"text-yellow-400 bg-yellow-900/30"),children:n.ledStatus.enhancedMode?"Enabled":"Manual"})]}),!n.ledStatus.enhancedMode&&i.jsxs("div",{className:"flex justify-between items-center",children:[i.jsx("span",{className:"text-slate-400",children:"Manual Intensity:"}),i.jsx("span",{className:"text-slate-200 font-mono",children:n.ledStatus.manualIntensity})]}),i.jsxs("div",{className:"flex justify-between items-center",children:[i.jsx("span",{className:"text-slate-400",children:"Scanning:"}),i.jsx("span",{className:"px-2 py-1 rounded text-xs "+(n.ledStatus.isScanning?"text-blue-400 bg-blue-900/30":"text-slate-400 bg-slate-700/50"),children:n.ledStatus.isScanning?"Active":"Idle"})]})]})]}),i.jsxs("div",{children:[i.jsx("h4",{className:"text-sm font-medium text-slate-300 mb-2",children:"Status Summary"}),i.jsxs("div",{className:"grid grid-cols-2 gap-2 text-xs",children:[i.jsxs("div",{className:`px-2 py-1 rounded ${p(n.statusIndicators.saturationStatus)}`,children:["Saturation: ",n.statusIndicators.saturationStatus]}),i.jsxs("div",{className:`px-2 py-1 rounded ${p(n.statusIndicators.signalStatus)}`,children:["Signal: ",n.statusIndicators.signalStatus]})]})]}),i.jsxs("div",{className:"text-xs text-slate-500 text-center",children:["Last updated: ",new Date(n.timestamp).toLocaleTimeString()]})]}):i.jsxs("div",{className:"text-center text-slate-400",children:[i.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400 mx-auto mb-2"}),"Loading sensor metrics..."]})]}):i.jsxs("div",{className:"bg-slate-800 rounded-lg p-6 border border-slate-600",children:[i.jsx("h3",{className:"text-lg font-semibold text-slate-200 mb-4",children:"Live Sensor Metrics"}),i.jsx("p",{className:"text-slate-400 text-center",children:"Monitoring disabled"})]})}const ne=({initialSettings:e,onSettingsChange:t,showToast:n})=>{var a;const[r,l]=f.useState((null==e?void 0:e.atime)||255),[s,o]=f.useState((null==e?void 0:e.again)||0),[u,c]=f.useState(Math.max(64,(null==e?void 0:e.brightness)||128)),[d,m]=f.useState((null==e?void 0:e.ledState)||!1),[p,h]=f.useState(!1),[g,b]=f.useState(!1),[v,x]=f.useState(void 0!==(null==e?void 0:e.autoZeroMode)?e.autoZeroMode:1),[y,w]=f.useState(void 0!==(null==e?void 0:e.autoZeroFreq)?e.autoZeroFreq:127),[S,k]=f.useState(void 0!==(null==e?void 0:e.waitTime)?e.waitTime:0),[N,j]=f.useState(!1),[C,E]=f.useState(!1),[T,P]=f.useState([]),[L,_]=f.useState(!1);f.useEffect((()=>{e&&(l(e.atime),o(e.again),c(e.brightness),m(e.ledState),x(void 0!==e.autoZeroMode?e.autoZeroMode:1),w(void 0!==e.autoZeroFreq?e.autoZeroFreq:127),k(void 0!==e.waitTime?e.waitTime:0))}),[e]);const M=f.useCallback(((e,t,n="")=>{const a={timestamp:(new Date).toLocaleTimeString(),test:e,success:t,message:n};P((e=>[...e,a]))}),[]),R=f.useCallback((()=>{P([])}),[]),A=e=>new Promise((t=>setTimeout(t,e))),$=e=>{const t=[];return void 0!==e.atime&&(e.atime<0||e.atime>255)&&t.push("ATIME must be 0-255"),void 0!==e.again&&(e.again<0||e.again>3)&&t.push("AGAIN must be 0-3"),void 0!==e.brightness&&(e.brightness<64||e.brightness>255)&&t.push("Brightness must be 64-255 (ESP32 hardware limit)"),void 0!==e.autoZeroFreq&&(e.autoZeroFreq<0||e.autoZeroFreq>255)&&t.push("Auto-Zero Frequency must be 0-255"),void 0!==e.waitTime&&(e.waitTime<0||e.waitTime>255)&&t.push("Wait Time must be 0-255"),void 0!==e.autoZeroMode&&(e.autoZeroMode<0||e.autoZeroMode>1)&&t.push("Auto-Zero Mode must be 0-1"),t},U=async e=>{try{M(e.name,!0,`Testing: ${JSON.stringify(e.settings)}`),await F(e.settings),await A(500);const t=await O();if(e.expectedValues){let n=!0;for(const[a,r]of Object.entries(e.expectedValues)){const l=t[a];l!==r&&(M(e.name,!1,`${a}: expected ${r}, got ${l}`),n=!1)}return!!n&&(M(e.name,!0,"All values applied correctly"),!0)}return e.shouldFail?(M(e.name,!0,"Invalid values correctly rejected"),!0):(M(e.name,!0,"Settings updated successfully"),!0)}catch(t){const n=t instanceof Error?t.message:"Unknown error";return e.shouldFail?(M(e.name,!0,`Correctly rejected: ${n}`),!0):(M(e.name,!1,`Failed: ${n}`),!1)}},V=async()=>{M("Valid Values Test",!0,"Starting valid values test...");const e=[{name:"Minimum Values",settings:{autoZeroMode:0,autoZeroFreq:0,waitTime:0},expectedValues:{autoZeroMode:0,autoZeroFreq:0,waitTime:0}},{name:"Recommended Values",settings:{autoZeroMode:1,autoZeroFreq:127,waitTime:5},expectedValues:{autoZeroMode:1,autoZeroFreq:127,waitTime:5}},{name:"Maximum Values",settings:{autoZeroMode:1,autoZeroFreq:255,waitTime:255},expectedValues:{autoZeroMode:1,autoZeroFreq:255,waitTime:255}}];let t=!0;for(const n of e){await U(n)||(t=!1),await A(300)}return M("Valid Values Test",t,t?"All valid values accepted":"Some valid values failed"),t},B=async()=>{M("Invalid Values Test",!0,"Starting invalid values test...");const e=await O(),t=[{name:"Invalid Auto-Zero Mode",settings:{autoZeroMode:5},shouldFail:!0},{name:"Invalid Auto-Zero Frequency",settings:{autoZeroFreq:300},shouldFail:!0},{name:"Invalid Wait Time",settings:{waitTime:300},shouldFail:!0}];let n=!0;for(const r of t){try{await F(r.settings),await A(500);const t=await O();e.autoZeroMode===t.autoZeroMode&&e.autoZeroFreq===t.autoZeroFreq&&e.waitTime===t.waitTime?M(r.name,!0,"Invalid values correctly rejected"):(M(r.name,!1,"Invalid values incorrectly accepted"),n=!1)}catch(a){M(r.name,!0,"Invalid values properly rejected by API")}await A(300)}return M("Invalid Values Test",n,n?"All invalid values rejected":"Some invalid values accepted"),n},q=async()=>{M("Combined Settings Test",!0,"Testing combined settings update...");const e={autoZeroMode:1,autoZeroFreq:127,waitTime:10,atime:56,again:3,brightness:128};try{await F(e),await A(1e3);const t=await O();let n=!0;for(const[a,r]of Object.entries(e)){const e=t[a];e!==r&&(M("Combined Settings Test",!1,`${a}: expected ${r}, got ${e}`),n=!1)}return!!n&&(M("Combined Settings Test",!0,"All combined settings applied correctly"),!0)}catch(t){const e=t instanceof Error?t.message:"Unknown error";return M("Combined Settings Test",!1,`Failed: ${e}`),!1}},W=[{value:0,label:"1x"},{value:1,label:"4x"},{value:2,label:"16x"},{value:3,label:"64x"}];return i.jsx(I,{title:"Scanner Settings",className:"mb-6",children:i.jsxs("div",{className:"space-y-6",children:[i.jsx(K,{id:"atime",label:"ATIME (Integration Time)",type:"number",min:"0",max:"255",value:r,onChange:e=>l(parseInt(e.target.value,10)),wrapperClassName:"max-w-xs"}),i.jsxs("div",{children:[i.jsx("label",{htmlFor:"again",className:"block text-sm font-medium text-slate-300 mb-1",children:"AGAIN (Analog Gain)"}),i.jsx("select",{id:"again",value:s,onChange:e=>o(parseInt(e.target.value,10)),className:"block w-full max-w-xs px-3 py-2 border border-slate-600 rounded-md bg-slate-700 text-slate-100 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",children:W.map((e=>i.jsx("option",{value:e.value,children:e.label},e.value)))})]}),i.jsx(X,{id:"scan-brightness",label:"Scan Brightness",min:64,max:255,value:u,onChange:c}),i.jsx(J,{id:"ledState",label:"LED Always On (Rainbow if idle)",checked:d,onChange:m}),i.jsx("div",{className:"border-t border-slate-600 pt-6",children:i.jsxs("div",{className:"space-y-4",children:[i.jsx("label",{className:"block text-sm font-medium text-slate-300 mb-3",children:"TCS3430 Advanced Calibration:"}),i.jsxs("div",{className:"bg-green-900/30 p-3 rounded-lg border border-green-600 mb-4",children:[i.jsx("p",{className:"text-sm font-medium text-green-300 mb-2",children:"📋 Recommended Optimal Settings:"}),i.jsxs("div",{className:"text-xs text-green-200 space-y-1",children:[i.jsxs("div",{children:[i.jsx("strong",{children:"ATIME:"})," 150 (Integration Time)"]}),i.jsxs("div",{children:[i.jsx("strong",{children:"AGAIN:"})," 16x (Analog Gain)"]}),i.jsxs("div",{children:[i.jsx("strong",{children:"Scan Brightness:"})," 128"]}),i.jsxs("div",{children:[i.jsx("strong",{children:"Auto-Zero Mode:"})," 1 (Use previous offset - recommended)"]}),i.jsxs("div",{children:[i.jsx("strong",{children:"Auto-Zero Frequency:"})," 127 (First cycle only - DFRobot recommended)"]}),i.jsxs("div",{children:[i.jsx("strong",{children:"Wait Time:"})," 0-10 (DFRobot default is 0, but low values for stability)"]})]}),i.jsx("p",{className:"text-xs text-slate-400 mt-2",children:"These settings provide optimal signal without saturation and are proven to work with Vivid White calibration."}),i.jsx(H,{onClick:()=>{l(150),o(2),c(128),x(1),w(127),k(5),n("Optimal settings applied! Remember to save settings.","success")},variant:"secondary",className:"text-xs px-3 py-1 mt-2",children:"Apply Optimal Settings"})]}),i.jsxs("div",{children:[i.jsx("label",{htmlFor:"autoZeroMode",className:"block text-sm font-medium text-slate-300 mb-1",children:"Auto-Zero Mode"}),i.jsxs("select",{id:"autoZeroMode",value:v.toString(),onChange:e=>x(parseInt(e.target.value,10)),className:"block w-full max-w-xs px-3 py-2 border border-slate-600 rounded-md bg-slate-700 text-slate-100 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",children:[i.jsx("option",{value:"0",children:"Always start at zero"}),i.jsx("option",{value:"1",children:"Use previous offset (recommended)"})]},`autoZeroMode-${v}`),i.jsx("p",{className:"text-xs text-slate-400 mt-1",children:"Mode 1 provides better stability by using previous calibration offset."})]}),i.jsx(X,{id:"autoZeroFreq",label:"Auto-Zero Frequency",min:0,max:255,value:y,onChange:w,unit:""}),i.jsx("p",{className:"text-xs text-slate-400 -mt-2",children:"0 = never, 127 = first cycle only (recommended), other values = every nth iteration"}),i.jsx(X,{id:"waitTime",label:"Wait Time",min:0,max:255,value:S,onChange:k,unit:""}),i.jsx("p",{className:"text-xs text-slate-400 -mt-2",children:"Wait time between measurements (0-255). Higher values improve stability but slow measurements."})]})}),i.jsx("div",{className:"border-t border-slate-600 pt-6",children:i.jsxs("div",{className:"space-y-4",children:[i.jsx("label",{className:"block text-sm font-medium text-slate-300",children:"White Point Calibration:"}),i.jsxs("div",{className:"bg-blue-900/30 p-3 rounded-lg border border-blue-600",children:[i.jsxs("p",{className:"text-sm text-blue-300 mb-3",children:["Calibrate white point using current sensor settings (ATIME: ",r,", AGAIN: ",null==(a=W[s])?void 0:a.label,", Brightness: ",u,")."]}),i.jsx("div",{className:"grid grid-cols-1 gap-2",children:i.jsx(H,{onClick:async()=>{b(!0);try{const e=await D(u);e.success?(n("✅ White point calibration completed successfully!","success"),t({})):n(`❌ White point calibration failed: ${e.message}`,"error")}catch(e){n(`❌ White point calibration failed: ${e instanceof Error?e.message:"Unknown error"}`,"error")}finally{b(!1)}},variant:"primary",size:"sm",isLoading:g,className:"w-full",children:g?"Calibrating...":"Calibrate White Point"})}),i.jsx("p",{className:"text-xs text-slate-400 mt-2",children:"Place Dulux Vivid White paint sample under sensor. Calibration takes 50 readings over ~5 seconds for accuracy."})]}),i.jsx("label",{className:"block text-sm font-medium text-slate-300 mt-6",children:"Black Point Calibration:"}),i.jsxs("div",{className:"bg-gray-900/50 p-3 rounded-lg border border-gray-600",children:[i.jsx("p",{className:"text-sm text-gray-300 mb-3",children:"Calibrate black point for dark reference measurement. LEDs will remain OFF during calibration."}),i.jsx("div",{className:"grid grid-cols-1 gap-2",children:i.jsx(H,{onClick:async()=>{b(!0);try{const e=await async function(){try{const e=await fetch(`${z}/calibrate/standard/black`,{method:"POST",headers:{"Content-Type":"application/json"}});if(!e.ok)throw new Error(`HTTP error! status: ${e.status}`);const t=await e.json();return{success:t.success,message:t.message||"Black calibration completed"}}catch(e){return{success:!1,message:e instanceof Error?e.message:"Black calibration failed"}}}();e.success?(n("✅ Black calibration completed successfully! LEDs were kept OFF during calibration.","success"),t({})):n(`❌ Black calibration failed: ${e.message}`,"error")}catch(e){n(`❌ Black calibration failed: ${e instanceof Error?e.message:"Unknown error"}`,"error")}finally{b(!1)}},variant:"secondary",size:"sm",isLoading:g,className:"w-full",children:g?"Calibrating...":"Calibrate Black Point"})}),i.jsx("p",{className:"text-xs text-slate-400 mt-2",children:"Cover sensor completely or place in dark environment. LEDs will be turned OFF automatically for dark reference measurement."})]})]})}),i.jsx("div",{className:"border-t border-slate-600 pt-6",children:i.jsx(ee,{showToast:n})}),i.jsx("div",{className:"border-t border-slate-600 pt-6",children:i.jsxs("div",{className:"space-y-4",children:[i.jsxs("div",{className:"flex items-center justify-between",children:[i.jsx("label",{className:"block text-sm font-medium text-slate-300",children:"🧪 Sensor Diagnostics & Testing"}),i.jsxs("div",{className:"flex gap-2",children:[i.jsx(H,{onClick:()=>window.open("./test_web_ui.html","_blank"),variant:"secondary",className:"text-xs px-3 py-1",title:"Open standalone test interface",children:"🔗 External Test"}),i.jsxs(H,{onClick:()=>j(!N),variant:"secondary",className:"text-xs px-3 py-1",children:[N?"Hide":"Show"," Tests"]})]})]}),N&&i.jsxs("div",{className:"space-y-4 bg-slate-800 p-4 rounded-lg border border-slate-600",children:[i.jsx("p",{className:"text-xs text-slate-400",children:"Comprehensive testing of TCS3430 advanced calibration settings. These tests validate parameter ranges, error handling, and sensor response."}),i.jsx("div",{className:"bg-slate-700 p-3 rounded border border-slate-600",children:i.jsxs("div",{className:"flex items-center justify-between",children:[i.jsxs("div",{children:[i.jsx("p",{className:"text-xs font-medium text-slate-200",children:"🔗 Standalone Test Interface"}),i.jsx("p",{className:"text-xs text-slate-400",children:"Open external test page with manual IP configuration"})]}),i.jsx(H,{onClick:()=>window.open("./test_web_ui.html","_blank"),variant:"primary",className:"text-xs px-3 py-1",children:"Open Test Page"})]})}),i.jsxs("div",{className:"space-y-3",children:[i.jsx(H,{onClick:async()=>{if(!C){E(!0),_(!0),M("Current Settings Test",!0,"Testing current form values...");try{const e={autoZeroMode:v,autoZeroFreq:y,waitTime:S,atime:r,again:s,brightness:u};await F(e),await A(500);const t=await O();let a=!0;for(const[n,r]of Object.entries(e)){const e=t[n];e!==r&&(M("Current Settings Test",!1,`${n}: expected ${r}, got ${e}`),a=!1)}a?(M("Current Settings Test",!0,"All current settings applied successfully"),n("Current settings test passed!","success")):n("Some current settings failed to apply","error")}catch(e){const t=e instanceof Error?e.message:"Unknown error";M("Current Settings Test",!1,`Failed: ${t}`),n("Current settings test failed","error")}finally{E(!1)}}},disabled:C,variant:"primary",className:"w-full text-sm",isLoading:C,children:"🔧 Test Current Settings"}),i.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3",children:[i.jsx(H,{onClick:V,disabled:C,variant:"secondary",className:"text-sm",children:"Test Valid Values"}),i.jsx(H,{onClick:B,disabled:C,variant:"secondary",className:"text-sm",children:"Test Invalid Values"}),i.jsx(H,{onClick:q,disabled:C,variant:"secondary",className:"text-sm",children:"Test Combined Settings"}),i.jsx(H,{onClick:async()=>{if(!C){E(!0),_(!0),R(),M("Test Suite",!0,"🚀 Starting comprehensive calibration test suite...");try{const e=await Promise.all([V(),B(),q()]),t=e.filter((e=>e)).length,a=e.length;M("Test Suite Complete",t===a,`🎯 Results: ${t}/${a} test groups passed`),t===a?n("🎉 All calibration tests passed!","success"):n(`⚠️ ${a-t} test group(s) failed`,"error")}catch(e){M("Test Suite",!1,`Test suite failed: ${e}`),n("Test suite encountered an error","error")}finally{E(!1)}}},disabled:C,variant:"primary",className:"text-sm",isLoading:C,children:"Run Full Test Suite"})]})]}),i.jsxs("div",{className:"flex items-center justify-between",children:[i.jsxs(H,{onClick:()=>_(!L),variant:"secondary",className:"text-xs px-3 py-1",disabled:0===T.length,children:[L?"Hide":"Show"," Results (",T.length,")"]}),T.length>0&&i.jsx(H,{onClick:R,variant:"secondary",className:"text-xs px-3 py-1 text-red-400 hover:text-red-300",children:"Clear Results"})]}),L&&T.length>0&&i.jsx("div",{className:"bg-slate-900 p-3 rounded border border-slate-700 max-h-64 overflow-y-auto",children:i.jsx("div",{className:"space-y-1 font-mono text-xs",children:T.map(((e,t)=>i.jsxs("div",{className:"flex items-start space-x-2 "+(e.success?"text-green-400":"text-red-400"),children:[i.jsxs("span",{className:"text-slate-500 min-w-[60px]",children:["[",e.timestamp,"]"]}),i.jsx("span",{className:"min-w-[20px]",children:e.success?"✅":"❌"}),i.jsxs("span",{className:"font-medium min-w-[120px]",children:[e.test,":"]}),i.jsx("span",{className:"text-slate-300 break-all",children:e.message})]},t)))})})]})]})}),i.jsxs("div",{className:"mt-6 p-4 bg-blue-900/30 border border-blue-700 rounded-lg",children:[i.jsx("h3",{className:"text-lg font-semibold text-blue-400 mb-2",children:"🔧 Enhanced LED Control"}),i.jsx("p",{className:"text-slate-300",children:"Enhanced LED Control functionality has been integrated into the Live LED Control component in the sidebar for better user experience."})]}),i.jsxs("div",{className:"mt-6 p-4 bg-green-900/30 border border-green-700 rounded-lg",children:[i.jsx("h3",{className:"text-lg font-semibold text-green-400 mb-2",children:"📊 Live Sensor Metrics"}),i.jsx("p",{className:"text-slate-300",children:"Component placement test - this should be visible"}),i.jsx(te,{isActive:!0,updateInterval:1500})]}),i.jsx("div",{className:"flex justify-end",children:i.jsx(H,{onClick:async()=>{h(!0);const e={atime:r,again:s,brightness:u,ledState:d,autoZeroMode:v,autoZeroFreq:y,waitTime:S},a=$(e);if(a.length>0)return n(`Invalid settings: ${a.join(", ")}`,"error"),void h(!1);try{await F(e),await new Promise((e=>setTimeout(e,750)));const a=await O(),i={atime:a.atime===r,again:a.again===s,brightness:a.brightness===u,autoZeroMode:a.autoZeroMode===v,autoZeroFreq:a.autoZeroFreq===y,waitTime:a.waitTime===S},d=Object.entries(i).filter((([e,t])=>!t)).map((([t,n])=>`${t} (expected: ${e[t]}, got: ${a[t]})`));0===d.length?(t(a),n("✅ All settings saved successfully!","success"),l(a.atime),o(a.again),c(a.brightness),x(a.autoZeroMode),w(a.autoZeroFreq),k(a.waitTime)):(n(`⚠️ Settings saved with changes: ${d.join(", ")}`,"error",8e3),l(a.atime),o(a.again),c(a.brightness),x(a.autoZeroMode),w(a.autoZeroFreq),k(a.waitTime),t(a))}catch(i){n(i instanceof Error?i.message:"Failed to save settings.","error")}finally{h(!1)}},isLoading:p,variant:"primary",children:"Save Settings"})})]})})},ae=({showToast:e,initialLedState:t})=>{var n;const[a,r]=f.useState(255),[l,s]=f.useState(255),[o,u]=f.useState(255),[c,d]=f.useState(128),[m,p]=f.useState(t||!1),[h,g]=f.useState(!1),[b,v]=f.useState(!1),[x,y]=f.useState(128),[w,S]=f.useState(!1),[k,N]=f.useState(null),[j,C]=f.useState(!1),[E,T]=f.useState(null),P=f.useRef(null),[_,M]=f.useState(128),[O,F]=f.useState(128),[D,A]=f.useState(0),[U,V]=f.useState([]);f.useEffect((()=>(B(),m&&W(),()=>Q())),[]),f.useEffect((()=>{m&&b?W():Q()}),[m,b]);const B=async()=>{try{const e=await fetch(`${z}${L}`,{method:"GET",headers:{Accept:"application/json"}});if(e.ok){const t=e.headers.get("content-type");if(t&&t.includes("application/json")){const t=await e.json();v(t.enhancedLEDMode??!1),y(t.manualLEDIntensity??128)}else S(!0)}}catch(e){S(!0)}},q=async()=>{try{const t={enhancedLEDMode:b,manualLEDIntensity:x},n=await fetch(`${z}${L}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(n.ok)e("Enhanced LED settings saved successfully!","success");else{const t=await n.text();e(`Failed to save settings: ${t}`,"error")}}catch(t){e(`Network error: ${t}`,"error")}},W=()=>{Q(),Z(),P.current=setInterval(Z,2e3)},Q=()=>{P.current&&(clearInterval(P.current),P.current=null)},Z=async()=>{try{const e=await fetch(`${z}/live-metrics`,{method:"GET",headers:{Accept:"application/json"}});if(!e.ok)throw new Error(`HTTP ${e.status}: ${e.statusText}`);{const t=e.headers.get("content-type");if(!t||!t.includes("application/json"))throw new Error("Live metrics endpoint not available - firmware update needed");{const t=await e.json();N(t),C(!0),T(null),b&&m&&t.sensorReadings&&G(t)}}}catch(e){C(!1),T(e instanceof Error?e.message:"Unknown error")}},G=async e=>{var t,n,a;const r=Date.now(),{x:l,y:s,z:i,ir:o}=e.sensorReadings,u=Math.max(l,s,i),c=(null==(t=e.metrics)?void 0:t.targetMin)||5e3,d=(null==(n=e.metrics)?void 0:n.targetMax)||6e4;let f=0;u<c?f=Math.min((c-u)/c,1):u>d&&(f=Math.min((u-d)/d,1));if(r-D<(f>.5?1e3:f>.2?2e3:f>0?3e3:5e3))return;const m=(null==(a=e.ledStatus)?void 0:a.currentBrightness)||_,p=u>0?o/u:0,h=p>.15;let g=m,b="";const v=.5+x/255*1.5,y=Math.round(40*(1+f)),w=Math.round(.05*(d-c)),S=c+w,k=d-w;if(u<S){const e=S-u,t=Math.min(Math.max(e/c,0),1),n=u<.5*c?1.5:1,a=Math.round(t*y*v*n),r=Math.max(2,Math.min(y,a));g=Math.min(255,m+r),b=`Low signal (${u} < ${S}), distance: ${e}, urgency: ${n.toFixed(1)}x, adj: +${r}`}else if(u>k){const e=u-k,t=Math.min(e/d,1),n=u>1.5*d?1.5:1,a=Math.round(t*y*v*n),r=Math.max(2,Math.min(y,a));g=Math.max(64,m-r),b=`High signal (${u} > ${k}), excess: ${e}, saturation: ${n.toFixed(1)}x, adj: -${r}`}else b=`Stable (${u} in range ${S}-${k})`;if(h&&g!==m){const e=Math.round(.3*Math.abs(g-m));g=Math.max(64,g-e),b+=` + IR compensation (${(100*p).toFixed(1)}%, -${e})`}const N=f>.3?.4*1.5:.4,j=N*g+(1-N)*O,C=Math.round(j);Math.abs(C-m)>1?(M(C),F(j),A(r),V((e=>[...e.slice(-9),C])),await Y(C,b)):F(j)},Y=async(e,t)=>{try{const t={brightness:e,r:a,g:l,b:o,keepOn:!0,enhancedMode:!0};(await R(t)).success}catch(n){}},J=async(t=!1)=>{g(!0),t&&b&&(v(!1),await q());const n=t?{brightness:0}:{brightness:b?_:c,r:a,g:l,b:o,keepOn:m,enhancedMode:b&&!t};try{const a=await R(n);a.success?(e(a.message||(t?"LED turned off.":"LED updated."),"success"),t?(p(!1),M(128),F(128),V([])):(p(!0),b&&(M(n.brightness),F(n.brightness)))):e(a.message||"Failed to update LED.","error")}catch(r){e(r instanceof Error?r.message:"Failed to update LED.","error")}finally{g(!1)}},ee=e=>{switch(e){case"optimal":case"normal":case"clean":case"adequate":return"text-green-400 bg-green-900/30";case"high":case"low":case"saturated":case"contaminated":return"text-red-400 bg-red-900/30";default:return"text-yellow-400 bg-yellow-900/30"}};return i.jsx(I,{title:"Live LED Control",className:"mb-6",children:i.jsxs("div",{className:"space-y-4",children:[w&&i.jsxs("div",{className:"mb-4 p-3 rounded-md text-sm bg-yellow-900/30 text-yellow-400 border border-yellow-700",children:[i.jsx("strong",{children:"Firmware Update Required:"})," Enhanced LED Control features require updated ESP32 firmware."]}),i.jsx("div",{className:"flex justify-center my-4",children:i.jsx($,{r:a,g:l,b:o,size:"lg"})}),i.jsxs("div",{className:"grid grid-cols-3 gap-2",children:[i.jsx(K,{id:"led-r",label:"R",type:"number",min:"0",max:"255",value:a,onChange:e=>r(Math.max(0,Math.min(255,parseInt(e.target.value)||0)))}),i.jsx(K,{id:"led-g",label:"G",type:"number",min:"0",max:"255",value:l,onChange:e=>s(Math.max(0,Math.min(255,parseInt(e.target.value)||0)))}),i.jsx(K,{id:"led-b",label:"B",type:"number",min:"0",max:"255",value:o,onChange:e=>u(Math.max(0,Math.min(255,parseInt(e.target.value)||0)))})]}),i.jsxs("div",{className:"flex items-center justify-between p-3 bg-slate-700/50 rounded-lg",children:[i.jsxs("div",{children:[i.jsx("label",{className:"text-sm font-medium text-slate-300",children:"Enhanced LED Control Mode"}),i.jsx("p",{className:"text-xs text-slate-500 mt-1",children:b?"Automatic brightness optimization for optimal sensor range (5,000-60,000)":"Manual LED intensity control"})]}),i.jsx("button",{onClick:async()=>{const e=!b;v(e),!e&&m&&(p(!1),await J(!0)),await q()},disabled:w,className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${b?"bg-blue-600":"bg-slate-600"} ${w?"opacity-50 cursor-not-allowed":""}`,children:i.jsx("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform "+(b?"translate-x-6":"translate-x-1")})})]}),i.jsx(X,{id:"live-brightness",label:b?"Optimization Sensitivity":"LED Brightness",min:b?50:0,max:255,value:b?x:c,onChange:b?y:d}),b&&i.jsxs("div",{className:"space-y-3 -mt-2",children:[i.jsxs("p",{className:"text-xs text-slate-500",children:["Sensitivity: ",x,"/255 - Higher values = more aggressive automatic adjustments"]}),i.jsxs("div",{className:"bg-gradient-to-r from-amber-900/20 to-orange-900/20 border border-amber-700/50 rounded-lg p-3",children:[i.jsxs("div",{className:"flex items-center justify-between mb-2",children:[i.jsx("h5",{className:"text-sm font-medium text-amber-300",children:"🔆 Real-time LED Brightness"}),i.jsxs("div",{className:"flex items-center space-x-2",children:[i.jsx("div",{className:"w-2 h-2 rounded-full "+(m?"bg-green-400":"bg-red-400")}),i.jsx("span",{className:"text-xs text-slate-400",children:m?"Active":"Inactive"})]})]}),m?i.jsxs("div",{className:"space-y-3",children:[i.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[i.jsxs("div",{className:"bg-slate-800/50 rounded p-2",children:[i.jsx("div",{className:"text-amber-300 text-xs font-medium",children:"Current PWM"}),i.jsx("div",{className:"text-amber-100 font-mono text-xl",children:_}),i.jsxs("div",{className:"text-amber-400 text-xs",children:[(_/255*100).toFixed(1),"% duty cycle"]})]}),i.jsxs("div",{className:"bg-slate-800/50 rounded p-2",children:[i.jsx("div",{className:"text-amber-300 text-xs font-medium",children:"Smoothed Target"}),i.jsx("div",{className:"text-amber-100 font-mono text-xl",children:Math.round(O)}),i.jsxs("div",{className:"text-amber-400 text-xs",children:[(Math.round(O)/255*100).toFixed(1),"% target"]})]})]}),U.length>0&&i.jsxs("div",{className:"bg-slate-800/50 rounded p-2",children:[i.jsx("div",{className:"text-amber-300 text-xs font-medium",children:"PWM History (Recent Adjustments)"}),i.jsx("div",{className:"text-amber-100 font-mono text-sm",children:U.slice(-5).join(" → ")}),i.jsxs("div",{className:"text-amber-400 text-xs mt-1",children:["Range: ",Math.min(...U.slice(-5))," - ",Math.max(...U.slice(-5)),"(",Math.max(...U.slice(-5))-Math.min(...U.slice(-5))," point spread)"]})]})]}):i.jsxs("div",{className:"text-center py-4",children:[i.jsx("div",{className:"text-slate-400 text-sm",children:"LED is OFF"}),i.jsx("div",{className:"text-slate-500 text-xs",children:'Click "Start Enhanced LED" to begin real-time monitoring'})]})]}),m&&i.jsx("div",{className:"text-xs text-slate-400 bg-slate-700/30 rounded p-2",children:i.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{children:"Auto Brightness:"}),i.jsxs("span",{className:"font-mono text-slate-200",children:[_,"/255"]})]}),i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{children:"EMA Smoothed:"}),i.jsxs("span",{className:"font-mono text-slate-200",children:[Math.round(O),"/255"]})]})]})})]}),b&&m&&i.jsxs("div",{className:"p-3 bg-slate-700/30 rounded-lg border border-slate-600",children:[i.jsxs("div",{className:"flex items-center justify-between mb-3",children:[i.jsx("h4",{className:"text-sm font-medium text-slate-300",children:"Live Sensor Feedback"}),i.jsxs("div",{className:"flex items-center space-x-2",children:[i.jsx("div",{className:"w-2 h-2 rounded-full "+(j?"bg-green-400":"bg-red-400")}),i.jsx("span",{className:"text-xs text-slate-400",children:j?"Connected":"Disconnected"})]})]}),E&&i.jsx("div",{className:"mb-3 p-2 rounded text-xs bg-red-900/30 text-red-400 border border-red-700",children:E}),k&&i.jsxs("div",{className:"space-y-3",children:[i.jsxs("div",{children:[i.jsx("h5",{className:"text-xs font-medium text-slate-300 mb-2",children:"TCS3430 Raw Channel Values"}),i.jsxs("div",{className:"grid grid-cols-3 gap-2 text-sm",children:[i.jsxs("div",{className:"bg-red-900/20 border border-red-700/50 rounded p-2",children:[i.jsx("div",{className:"text-red-300 text-xs font-medium",children:"R (Red)"}),i.jsx("div",{className:"text-red-100 font-mono text-sm",children:k.sensorReadings.x})]}),i.jsxs("div",{className:"bg-green-900/20 border border-green-700/50 rounded p-2",children:[i.jsx("div",{className:"text-green-300 text-xs font-medium",children:"G (Green)"}),i.jsx("div",{className:"text-green-100 font-mono text-sm",children:k.sensorReadings.y})]}),i.jsxs("div",{className:"bg-blue-900/20 border border-blue-700/50 rounded p-2",children:[i.jsx("div",{className:"text-blue-300 text-xs font-medium",children:"B (Blue)"}),i.jsx("div",{className:"text-blue-100 font-mono text-sm",children:k.sensorReadings.z})]}),i.jsxs("div",{className:"bg-slate-800/50 border border-slate-600 rounded p-2",children:[i.jsx("div",{className:"text-slate-300 text-xs font-medium",children:"C (Clear)"}),i.jsx("div",{className:"text-slate-100 font-mono text-sm",children:k.sensorReadings.x+k.sensorReadings.y+k.sensorReadings.z})]}),i.jsxs("div",{className:"bg-purple-900/20 border border-purple-700/50 rounded p-2",children:[i.jsx("div",{className:"text-purple-300 text-xs font-medium",children:"IR"}),i.jsx("div",{className:"text-purple-100 font-mono text-sm",children:k.sensorReadings.ir})]}),i.jsxs("div",{className:"bg-slate-800/50 border border-slate-600 rounded p-2",children:[i.jsx("div",{className:"text-slate-300 text-xs font-medium",children:"Status"}),i.jsxs("div",{className:"text-slate-100 font-mono text-sm",children:["0x",null==(n=k.sensorReadings.status)?void 0:n.toString(16).toUpperCase().padStart(2,"0")]})]})]})]}),i.jsxs("div",{className:"grid grid-cols-2 gap-3 text-sm",children:[i.jsxs("div",{className:"bg-slate-800/50 rounded p-2",children:[i.jsx("span",{className:"text-slate-400",children:"Current Brightness:"}),i.jsx("span",{className:"text-slate-200 ml-2 font-mono",children:k.ledStatus.currentBrightness})]}),i.jsxs("div",{className:"bg-slate-800/50 rounded p-2",children:[i.jsx("span",{className:"text-slate-400",children:"Control Variable:"}),i.jsxs("div",{className:"flex items-center space-x-1 mt-1",children:[i.jsx("span",{className:"text-slate-200 font-mono text-xs",children:k.metrics.controlVariable}),i.jsx("span",{className:`px-1 py-0.5 rounded text-xs ${ee(k.statusIndicators.controlVariableStatus)}`,children:k.statusIndicators.controlVariableStatus})]})]})]}),i.jsxs("div",{className:"text-xs",children:[i.jsxs("div",{className:"flex justify-between items-center",children:[i.jsx("span",{className:"text-slate-400",children:"Target Range:"}),i.jsxs("span",{className:"text-slate-300",children:[k.metrics.targetMin," - ",k.metrics.targetMax]})]}),i.jsxs("div",{className:"flex justify-between items-center mt-1",children:[i.jsx("span",{className:"text-slate-400",children:"In Optimal Range:"}),i.jsx("span",{className:"px-2 py-0.5 rounded text-xs "+(k.metrics.inOptimalRange?"text-green-400 bg-green-900/30":"text-yellow-400 bg-yellow-900/30"),children:k.metrics.inOptimalRange?"Yes":"No"})]}),i.jsxs("div",{className:"flex justify-between items-center mt-1",children:[i.jsx("span",{className:"text-slate-400",children:"Auto-Optimization:"}),i.jsx("span",{className:"px-2 py-0.5 rounded text-xs "+(b&&m?"text-blue-400 bg-blue-900/30":"text-slate-400 bg-slate-700/30"),children:b&&m?"Active":"Inactive"})]}),b&&m&&i.jsxs("div",{className:"flex justify-between items-center mt-1",children:[i.jsx("span",{className:"text-slate-400",children:"IR Ratio:"}),i.jsxs("span",{className:"px-1 py-0.5 rounded text-xs "+(k.metrics.irRatio>.15?"text-red-400 bg-red-900/30":"text-green-400 bg-green-900/30"),children:[(100*k.metrics.irRatio).toFixed(1),"%"]})]})]}),i.jsxs("div",{className:"grid grid-cols-2 gap-2 text-xs",children:[i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-slate-400",children:"Saturation:"}),i.jsx("span",{className:`px-1 py-0.5 rounded ${ee(k.statusIndicators.saturationStatus)}`,children:k.statusIndicators.saturationStatus})]}),i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-slate-400",children:"IR Status:"}),i.jsx("span",{className:`px-1 py-0.5 rounded ${ee(k.statusIndicators.irContaminationStatus)}`,children:k.statusIndicators.irContaminationStatus})]})]})]})]}),i.jsxs("div",{className:"flex space-x-2 mt-4",children:[i.jsx(H,{onClick:()=>J(!1),isLoading:h,variant:"primary",className:"flex-1",disabled:b&&!m,children:b?"Start Enhanced LED":"Set LED"}),i.jsx(H,{onClick:()=>J(!0),isLoading:h,variant:"secondary",className:"flex-1",children:"Turn LED Off"})]}),b&&i.jsxs("div",{className:"p-3 bg-blue-900/20 rounded-lg border border-blue-700/50",children:[i.jsx("h4",{className:"text-sm font-medium text-blue-300 mb-2",children:"Enhanced Mode Features"}),i.jsxs("ul",{className:"text-xs text-blue-200 space-y-1",children:[i.jsx("li",{children:"• Automatic brightness optimization using max(R,G,B) control variable"}),i.jsx("li",{children:"• Target range: 5,000 - 60,000 for optimal sensor readings"}),i.jsx("li",{children:"• IR contamination detection and compensation (threshold: 15%)"}),i.jsx("li",{children:"• Exponential moving average smoothing to prevent flickering"}),i.jsx("li",{children:"• Sensitivity-based adjustment scaling (0.5x - 2.0x multiplier)"}),i.jsx("li",{children:"• 3-second throttling between automatic adjustments"})]}),m&&i.jsxs("div",{className:"mt-3 pt-2 border-t border-blue-700/30",children:[i.jsxs("p",{className:"text-xs text-blue-300",children:[i.jsx("strong",{children:"Status:"})," Auto-optimization is ",b&&m?"ACTIVE":"INACTIVE"]}),i.jsx("p",{className:"text-xs text-blue-400 mt-1",children:"Adjust sensitivity slider to control how aggressively the system optimizes brightness."})]})]})]})})},re=()=>{const[e,t]=f.useState(null),[n,a]=f.useState([]),[r,l]=f.useState(!0),[s,o]=f.useState(!0),[u,c]=f.useState(null),[d,m]=f.useState(null),[p,h]=f.useState(null),g=(e,t,n)=>{h({message:e,type:t,id:Date.now()});const a=e.includes("Calibration values too high (saturated)");setTimeout((()=>h(null)),n||(a?13e3:3e3))},b=f.useCallback((async()=>{l(!0),c(null);try{const e=await O();t(e)}catch(e){c(e instanceof Error?e.message:"Failed to load device status."),g(e instanceof Error?e.message:"Failed to load device status.","error")}finally{l(!1)}}),[]),v=f.useCallback((async()=>{o(!0),m(null);try{const e=(await async function(){return M(await fetch(`${_}/samples`))}()).samples.sort(((e,t)=>t.timestamp-e.timestamp));a(e)}catch(e){m(e instanceof Error?e.message:"Failed to load samples."),g(e instanceof Error?e.message:"Failed to load samples.","error")}finally{o(!1)}}),[]);f.useEffect((()=>{b(),v();const e=setInterval(b,3e4);return()=>clearInterval(e)}),[]);const x=()=>{v(),b()};return i.jsxs("div",{className:"min-h-screen bg-slate-900 p-4 md:p-8",children:[p&&i.jsx("div",{className:`fixed top-5 right-5 p-4 rounded-md shadow-lg text-white ${p.message.includes("Calibration values too high (saturated)")?"animate-fadeInOutBackLong":"animate-fadeInOutBack"} ${"success"===p.type?"bg-green-600":"bg-red-600"}`,style:{zIndex:1e3},children:p.message}),i.jsx("header",{className:"mb-8",children:i.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between",children:[i.jsxs("div",{className:"text-center sm:text-left",children:[i.jsx("h1",{className:"text-4xl font-bold text-slate-100",children:"ESP32 Color Matcher"}),i.jsx("p",{className:"text-slate-400",children:"Web Interface"})]}),i.jsx("nav",{className:"mt-4 sm:mt-0 flex justify-center sm:justify-end",children:i.jsxs("a",{href:`${z}${L}`,target:"_blank",rel:"noopener noreferrer","aria-label":"Open device settings (external link)",className:"flex items-center gap-1.5 px-3 py-2 sm:px-4 sm:py-2 rounded-md text-sm font-medium text-amber-700 bg-amber-100 hover:bg-amber-200 hover:shadow-md transition-all duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-amber-400 border border-amber-300",children:[i.jsx("span",{className:"hidden sm:inline",children:"Device Settings"}),i.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",className:"w-5 h-5",children:[i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.646.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 0 1 0 1.905c-.007.379.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 0 1-.22.128c-.333.184-.582.496-.646.87l-.212 1.282c-.09.542-.56.94-1.11.94h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.759 6.759 0 0 1 0-1.905c.007-.379-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.184.582-.496.644-.87l.214-1.282Z"}),i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"})]})]})})]})}),i.jsxs("main",{className:"max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-3 gap-6",children:[i.jsxs("section",{className:"lg:col-span-2 space-y-6",children:[i.jsx(B,{status:e,isLoading:r,error:u,onRefresh:b}),i.jsx(q,{onSampleSaved:x,showToast:g}),i.jsx(Z,{}),i.jsx(Y,{samples:n,isLoading:s,error:d,onSampleDeleted:x,showToast:g})]}),i.jsxs("aside",{className:"lg:col-span-1 space-y-6",children:[e&&i.jsx(ae,{showToast:g,initialLedState:null==e?void 0:e.ledState}),i.jsx(ne,{initialSettings:e,onSettingsChange:e=>{t((t=>t?{...t,...e}:null))},showToast:g})]})]}),i.jsx("footer",{className:"mt-12 text-center text-sm text-slate-400",children:i.jsxs("p",{children:["© ",(new Date).getFullYear()," ESP32 Color Matcher Interface. For device firmware version X.Y.Z."]})}),i.jsx("style",{children:"\n        @keyframes fadeInOutBack {\n          0% { opacity: 0; transform: translateY(-20px); }\n          10%, 90% { opacity: 1; transform: translateY(0); }\n          100% { opacity: 0; transform: translateY(-20px); }\n        }\n        @keyframes fadeInOutBackLong {\n          0% { opacity: 0; transform: translateY(-20px); }\n          5%, 95% { opacity: 1; transform: translateY(0); }\n          100% { opacity: 0; transform: translateY(-20px); }\n        }\n        .animate-fadeInOutBack {\n          animation: fadeInOutBack 3s ease-in-out forwards;\n        }\n        .animate-fadeInOutBackLong {\n          animation: fadeInOutBackLong 13s ease-in-out forwards;\n        }\n      "})]})},le=document.getElementById("root");if(!le)throw new Error("Could not find root element to mount to");P.createRoot(le).render(i.jsx(m.StrictMode,{children:i.jsx(re,{})}));
