#include "AdvancedTCS3430Calibration.h"
#include <math.h>
#include <algorithm>

// ============================================================================
// LOGGING MACROS
// ============================================================================
#define LOG_CAL_ERROR(format, ...) Serial.printf("[CAL ERROR] " format "\n", ##__VA_ARGS__)
#define LOG_CAL_WARN(format, ...)  Serial.printf("[CAL WARN] " format "\n", ##__VA_ARGS__)
#define LOG_CAL_INFO(format, ...)  Serial.printf("[CAL INFO] " format "\n", ##__VA_ARGS__)
#define LOG_CAL_DEBUG(format, ...) Serial.printf("[CAL DEBUG] " format "\n", ##__VA_ARGS__)

// ============================================================================
// CONSTRUCTOR AND INITIALIZATION
// ============================================================================

AdvancedTCS3430Calibration::AdvancedTCS3430Calibration(DFRobot_TCS3430* sensor, int ledPin) 
    : tcs3430(sensor), ledPin(ledPin), currentLEDBrightness(128), initialized(false), 
      numPatches(0), hasWhitePoint(false), hasBlackPoint(false) {
    initializeDefaults();
}

AdvancedTCS3430Calibration::~AdvancedTCS3430Calibration() {
    if (initialized) {
        preferences.end();
    }
}

bool AdvancedTCS3430Calibration::initialize() {
    if (!tcs3430) {
        LOG_CAL_ERROR("TCS3430 sensor pointer is null");
        return false;
    }
    
    // Initialize preferences
    if (!preferences.begin("adv_cal", false)) {
        LOG_CAL_ERROR("Failed to initialize NVS preferences");
        return false;
    }
    
    // Initialize LED pin
    pinMode(ledPin, OUTPUT);
    setLEDBrightness(currentLEDBrightness);
    
    // Initialize PID controller with optimized parameters
    initializePIDController(2.0f, 0.1f, 0.05f);
    
    // Load existing calibration data
    loadCalibrationData();
    
    initialized = true;
    LOG_CAL_INFO("Advanced TCS3430 calibration system initialized");
    return true;
}

void AdvancedTCS3430Calibration::initializeDefaults() {
    // Initialize calibration matrix
    calibrationMatrix.valid = false;
    calibrationMatrix.quality = 0.0f;
    calibrationMatrix.irThreshold = 0.3f;
    
    // Clear matrix to identity
    for (int i = 0; i < MATRIX_4X4_SIZE; i++) {
        calibrationMatrix.matrix[i] = (i % 5 == 0) ? 1.0f : 0.0f; // Identity matrix
    }
    
    // Initialize scaling factors
    calibrationMatrix.scaling[0] = 1.0f;
    calibrationMatrix.scaling[1] = 1.0f;
    calibrationMatrix.scaling[2] = 1.0f;
    
    // Initialize IR compensation parameters
    irThreshold = 0.3f;
    irWeightLow = 0.1f;
    irWeightHigh = 0.8f;
    
    // Clear patches
    numPatches = 0;
    for (int i = 0; i < MAX_CAL_POINTS; i++) {
        patches[i].valid = false;
    }
}

void AdvancedTCS3430Calibration::reset() {
    initializeDefaults();
    clearCalibrationPatches();
    hasWhitePoint = false;
    hasBlackPoint = false;
    LOG_CAL_INFO("Calibration system reset to defaults");
}

// ============================================================================
// ADVANCED SENSOR READING WITH NOISE REDUCTION
// ============================================================================

AdvancedTCS3430Calibration::RawSensorData AdvancedTCS3430Calibration::readSingleSample() {
    RawSensorData data;
    
    // Ensure sensor is ready
    delay(10);
    
    // Read all channels (using correct DFRobot_TCS3430 API)
    data.r = tcs3430->getXData();  // X channel maps to red
    data.g = tcs3430->getYData();  // Y channel maps to green
    data.b = tcs3430->getZData();  // Z channel maps to blue
    data.c = tcs3430->getYData();  // Clear channel (use Y as approximation)
    data.ir1 = tcs3430->getIR1Data();
    data.ir2 = tcs3430->getIR2Data();
    
    // Add timestamp
    data.timestamp = millis();
    
    // Read temperature if available (placeholder)
    data.temperature = 25.0f; // Default room temperature
    
    return data;
}

AdvancedTCS3430Calibration::RawSensorData AdvancedTCS3430Calibration::readAveragedSensorData(
    int numSamples, int stabilizationDelay) {
    
    if (numSamples <= 0) numSamples = NUM_SAMPLES_DEFAULT;
    
    RawSensorData samples[numSamples];
    
    // Collect samples
    for (int i = 0; i < numSamples; i++) {
        samples[i] = readSingleSample();
        if (i < numSamples - 1) {
            delay(stabilizationDelay);
        }
    }
    
    // Apply statistical filtering
    return applyStatisticalFiltering(samples, numSamples);
}

AdvancedTCS3430Calibration::RawSensorData AdvancedTCS3430Calibration::applyStatisticalFiltering(
    RawSensorData samples[], int count) {
    
    if (count <= 0) return samples[0];
    if (count == 1) return samples[0];
    
    // For small sample sizes, use simple averaging
    if (count <= 3) {
        RawSensorData avg = {0};
        for (int i = 0; i < count; i++) {
            avg.r += samples[i].r;
            avg.g += samples[i].g;
            avg.b += samples[i].b;
            avg.c += samples[i].c;
            avg.ir1 += samples[i].ir1;
            avg.ir2 += samples[i].ir2;
            avg.temperature += samples[i].temperature;
        }
        
        avg.r /= count;
        avg.g /= count;
        avg.b /= count;
        avg.c /= count;
        avg.ir1 /= count;
        avg.ir2 /= count;
        avg.temperature /= count;
        avg.timestamp = samples[count-1].timestamp;
        
        return avg;
    }
    
    // For larger samples, use trimmed mean (remove outliers)
    auto trimmedMean = [](uint16_t values[], int n) -> uint16_t {
        std::sort(values, values + n);
        int trimCount = n / 10; // Remove 10% from each end
        if (trimCount < 1) trimCount = 1;
        
        uint32_t sum = 0;
        int validCount = 0;
        for (int i = trimCount; i < n - trimCount; i++) {
            sum += values[i];
            validCount++;
        }
        return validCount > 0 ? sum / validCount : values[n/2];
    };
    
    // Extract values for trimmed mean calculation
    uint16_t r_vals[count], g_vals[count], b_vals[count], c_vals[count];
    uint16_t ir1_vals[count], ir2_vals[count];
    float temp_vals[count];
    
    for (int i = 0; i < count; i++) {
        r_vals[i] = samples[i].r;
        g_vals[i] = samples[i].g;
        b_vals[i] = samples[i].b;
        c_vals[i] = samples[i].c;
        ir1_vals[i] = samples[i].ir1;
        ir2_vals[i] = samples[i].ir2;
        temp_vals[i] = samples[i].temperature;
    }
    
    RawSensorData result;
    result.r = trimmedMean(r_vals, count);
    result.g = trimmedMean(g_vals, count);
    result.b = trimmedMean(b_vals, count);
    result.c = trimmedMean(c_vals, count);
    result.ir1 = trimmedMean(ir1_vals, count);
    result.ir2 = trimmedMean(ir2_vals, count);
    
    // Average temperature
    float tempSum = 0;
    for (int i = 0; i < count; i++) {
        tempSum += temp_vals[i];
    }
    result.temperature = tempSum / count;
    result.timestamp = samples[count-1].timestamp;
    
    return result;
}

// ============================================================================
// PID LED BRIGHTNESS CONTROL
// ============================================================================

void AdvancedTCS3430Calibration::initializePIDController(float kp, float ki, float kd) {
    pidController.kp = kp;
    pidController.ki = ki;
    pidController.kd = kd;
    pidController.integral = 0.0f;
    pidController.lastError = 0.0f;
    pidController.outputMin = 64.0f;   // Minimum LED brightness
    pidController.outputMax = 255.0f;  // Maximum LED brightness
    pidController.lastTime = millis();
    
    LOG_CAL_INFO("PID controller initialized: Kp=%.2f, Ki=%.2f, Kd=%.2f", kp, ki, kd);
}

uint8_t AdvancedTCS3430Calibration::updateLEDBrightnessPID(float targetValue) {
    // Read current sensor value
    RawSensorData current = readSingleSample();
    float currentValue = std::max({(float)current.r, (float)current.g, (float)current.b});
    
    // Calculate time delta
    uint32_t currentTime = millis();
    float deltaTime = (currentTime - pidController.lastTime) / 1000.0f; // Convert to seconds
    pidController.lastTime = currentTime;
    
    // Avoid division by zero
    if (deltaTime <= 0) deltaTime = 0.01f;
    
    // Calculate error
    float error = targetValue - currentValue;
    
    // Proportional term
    float proportional = pidController.kp * error;
    
    // Integral term (with windup protection)
    pidController.integral += error * deltaTime;
    // Clamp integral to prevent windup
    float maxIntegral = 100.0f;
    pidController.integral = std::max(-maxIntegral, std::min(maxIntegral, pidController.integral));
    float integral = pidController.ki * pidController.integral;
    
    // Derivative term
    float derivative = pidController.kd * (error - pidController.lastError) / deltaTime;
    pidController.lastError = error;
    
    // Calculate output
    float output = proportional + integral + derivative;
    
    // Convert to brightness adjustment
    float newBrightness = currentLEDBrightness + output;
    
    // Clamp to valid range
    newBrightness = std::max(pidController.outputMin, std::min(pidController.outputMax, newBrightness));
    
    // Apply new brightness
    uint8_t brightness = (uint8_t)newBrightness;
    setLEDBrightness(brightness);
    
    LOG_CAL_DEBUG("PID: Target=%.0f, Current=%.0f, Error=%.0f, Output=%.0f, Brightness=%d", 
                  targetValue, currentValue, error, output, brightness);
    
    return brightness;
}

void AdvancedTCS3430Calibration::setLEDBrightness(uint8_t brightness) {
    // Clamp to valid range (ESP32 PWM limitation)
    brightness = std::max((uint8_t)64, std::min((uint8_t)255, brightness));
    
    currentLEDBrightness = brightness;
    analogWrite(ledPin, brightness);
    
    // Allow LED to stabilize
    delay(10);
}

// ============================================================================
// DYNAMIC IR COMPENSATION
// ============================================================================

float AdvancedTCS3430Calibration::applyDynamicIRCompensation(const RawSensorData& raw, 
                                                           float& x, float& y, float& z) {
    // Calculate IR contamination level
    float irContamination = calculateIRContamination(raw);
    
    // Apply compensation based on contamination level
    float compensationFactor = 1.0f;
    
    if (irContamination > irThreshold) {
        // High IR contamination - apply stronger compensation
        compensationFactor = 1.0f - (irContamination * irWeightHigh);
        
        // Reduce RGB values proportionally to IR contamination
        x *= compensationFactor;
        y *= compensationFactor;
        z *= compensationFactor;
        
        LOG_CAL_DEBUG("High IR compensation applied: contamination=%.3f, factor=%.3f", 
                      irContamination, compensationFactor);
    } else if (irContamination > 0.1f) {
        // Moderate IR contamination - apply light compensation
        compensationFactor = 1.0f - (irContamination * irWeightLow);
        
        x *= compensationFactor;
        y *= compensationFactor;
        z *= compensationFactor;
        
        LOG_CAL_DEBUG("Light IR compensation applied: contamination=%.3f, factor=%.3f", 
                      irContamination, compensationFactor);
    }
    
    return compensationFactor;
}

float AdvancedTCS3430Calibration::calculateIRContamination(const RawSensorData& raw) {
    // Calculate total visible light
    float visibleLight = raw.r + raw.g + raw.b;
    
    // Calculate total IR
    float totalIR = raw.ir1 + raw.ir2;
    
    // Avoid division by zero
    if (visibleLight + totalIR <= 0) return 0.0f;
    
    // Calculate IR contamination ratio
    float irRatio = totalIR / (visibleLight + totalIR);
    
    // Normalize to 0-1 range
    return std::min(1.0f, std::max(0.0f, irRatio));
}

void AdvancedTCS3430Calibration::setIRCompensationParameters(float threshold, 
                                                           float lowWeight, float highWeight) {
    irThreshold = std::max(0.0f, std::min(1.0f, threshold));
    irWeightLow = std::max(0.0f, std::min(1.0f, lowWeight));
    irWeightHigh = std::max(0.0f, std::min(1.0f, highWeight));
    
    LOG_CAL_INFO("IR compensation parameters updated: threshold=%.3f, low=%.3f, high=%.3f",
                 irThreshold, irWeightLow, irWeightHigh);
}

// ============================================================================
// TIKHONOV REGULARIZED MATRIX CALIBRATION
// ============================================================================

bool AdvancedTCS3430Calibration::computeRegularizedMatrix(CalibrationPatch patches[],
                                                         int numPatches, float lambda) {
    if (numPatches < 4) {
        LOG_CAL_ERROR("Insufficient calibration patches: %d (minimum 4 required)", numPatches);
        return false;
    }

    LOG_CAL_INFO("Computing regularized calibration matrix with %d patches, lambda=%.4f",
                 numPatches, lambda);

    // Create matrices for least squares: X * M = Y
    // X is (n x 4) matrix of [R, G, B, 1]
    // Y is (n x 3) matrix of [X, Y, Z] reference values
    // M is (4 x 3) transformation matrix we want to solve for

    const int n = numPatches;
    const int inputDim = 4;  // R, G, B, 1
    const int outputDim = 3; // X, Y, Z

    // Allocate matrices
    float* X = new float[n * inputDim];
    float* Y = new float[n * outputDim];
    float* XtX = new float[inputDim * inputDim];
    float* XtY = new float[inputDim * outputDim];
    float* regularizedXtX = new float[inputDim * inputDim];
    float* M = new float[inputDim * outputDim];

    // Fill X matrix (sensor readings)
    for (int i = 0; i < n; i++) {
        X[i * inputDim + 0] = patches[i].raw.r;
        X[i * inputDim + 1] = patches[i].raw.g;
        X[i * inputDim + 2] = patches[i].raw.b;
        X[i * inputDim + 3] = 1.0f; // Bias term
    }

    // Fill Y matrix (reference XYZ values)
    for (int i = 0; i < n; i++) {
        Y[i * outputDim + 0] = patches[i].ref_x;
        Y[i * outputDim + 1] = patches[i].ref_y;
        Y[i * outputDim + 2] = patches[i].ref_z;
    }

    // Compute X^T * X
    matrixMultiply(X, X, XtX, inputDim, n, inputDim); // X^T * X

    // Add regularization: X^T * X + lambda * I
    for (int i = 0; i < inputDim; i++) {
        for (int j = 0; j < inputDim; j++) {
            regularizedXtX[i * inputDim + j] = XtX[i * inputDim + j];
            if (i == j) {
                regularizedXtX[i * inputDim + j] += lambda; // Add lambda to diagonal
            }
        }
    }

    // Compute X^T * Y
    matrixMultiply(X, Y, XtY, inputDim, n, outputDim); // X^T * Y

    // Solve (X^T * X + lambda * I) * M = X^T * Y for M
    bool success = solveLinearSystem(regularizedXtX, XtY, M, inputDim, outputDim, 0.0f);

    if (success) {
        // Store the computed matrix (expand 4x3 to 4x4 for homogeneous coordinates)
        for (int i = 0; i < inputDim; i++) {
            for (int j = 0; j < outputDim; j++) {
                calibrationMatrix.matrix[i * 4 + j] = M[i * outputDim + j];
            }
            // Set the 4th column to [0, 0, 0, 1] for homogeneous coordinates
            calibrationMatrix.matrix[i * 4 + 3] = (i == 3) ? 1.0f : 0.0f;
        }

        calibrationMatrix.valid = true;
        calibrationMatrix.quality = validateCalibrationQuality();

        LOG_CAL_INFO("Matrix computation successful, quality=%.3f", calibrationMatrix.quality);

        // Log the computed matrix for debugging
        LOG_CAL_DEBUG("Computed calibration matrix:");
        for (int i = 0; i < 4; i++) {
            LOG_CAL_DEBUG("  [%.4f, %.4f, %.4f, %.4f]",
                         calibrationMatrix.matrix[i*4+0], calibrationMatrix.matrix[i*4+1],
                         calibrationMatrix.matrix[i*4+2], calibrationMatrix.matrix[i*4+3]);
        }
    } else {
        LOG_CAL_ERROR("Matrix computation failed - singular matrix or numerical instability");
        calibrationMatrix.valid = false;
        calibrationMatrix.quality = 0.0f;
    }

    // Clean up
    delete[] X;
    delete[] Y;
    delete[] XtX;
    delete[] XtY;
    delete[] regularizedXtX;
    delete[] M;

    return success;
}

bool AdvancedTCS3430Calibration::addCalibrationPatch(const RawSensorData& raw,
                                                    float ref_x, float ref_y, float ref_z,
                                                    const char* name) {
    if (numPatches >= MAX_CAL_POINTS) {
        LOG_CAL_ERROR("Maximum calibration patches reached: %d", MAX_CAL_POINTS);
        return false;
    }

    CalibrationPatch& patch = patches[numPatches];
    patch.raw = raw;
    patch.ref_x = ref_x;
    patch.ref_y = ref_y;
    patch.ref_z = ref_z;

    // Convert reference XYZ to sRGB for Delta E calculation
    xyzToSRGB(ref_x, ref_y, ref_z, patch.ref_r, patch.ref_g, patch.ref_b);

    // Copy name (ensure null termination)
    strncpy(patch.name, name, sizeof(patch.name) - 1);
    patch.name[sizeof(patch.name) - 1] = '\0';

    patch.valid = true;
    patch.deltaE = 0.0f; // Will be calculated during validation

    numPatches++;

    LOG_CAL_INFO("Added calibration patch '%s': XYZ(%.1f,%.1f,%.1f) RGB(%d,%d,%d)",
                 name, ref_x, ref_y, ref_z, patch.ref_r, patch.ref_g, patch.ref_b);

    return true;
}

void AdvancedTCS3430Calibration::clearCalibrationPatches() {
    numPatches = 0;
    for (int i = 0; i < MAX_CAL_POINTS; i++) {
        patches[i].valid = false;
    }
    LOG_CAL_INFO("Cleared all calibration patches");
}

float AdvancedTCS3430Calibration::validateCalibrationQuality() {
    if (!calibrationMatrix.valid || numPatches == 0) {
        return 0.0f;
    }

    float totalDeltaE = 0.0f;
    int validPatches = 0;

    for (int i = 0; i < numPatches; i++) {
        if (!patches[i].valid) continue;

        // Apply calibration matrix to raw sensor data
        CalibratedData calibrated = rawToXYZ(patches[i].raw);

        // Convert both reference and calibrated to LAB for Delta E calculation
        float ref_L, ref_a, ref_b;
        float cal_L, cal_a, cal_b;

        srgbToLab(patches[i].ref_r, patches[i].ref_g, patches[i].ref_b, ref_L, ref_a, ref_b);
        srgbToLab(calibrated.r_srgb, calibrated.g_srgb, calibrated.b_srgb, cal_L, cal_a, cal_b);

        // Calculate Delta E 2000
        float deltaE = calculateDeltaE2000(ref_L, ref_a, ref_b, cal_L, cal_a, cal_b);

        patches[i].deltaE = deltaE;
        totalDeltaE += deltaE;
        validPatches++;

        LOG_CAL_DEBUG("Patch '%s': Delta E = %.2f", patches[i].name, deltaE);
    }

    if (validPatches == 0) return 0.0f;

    float avgDeltaE = totalDeltaE / validPatches;

    // Convert Delta E to quality score (0.0 = poor, 1.0 = excellent)
    // Delta E < 1: Excellent (quality > 0.9)
    // Delta E < 2: Very good (quality > 0.8)
    // Delta E < 5: Acceptable (quality > 0.5)
    // Delta E > 10: Poor (quality < 0.2)

    float quality;
    if (avgDeltaE < 1.0f) {
        quality = 0.9f + (1.0f - avgDeltaE) * 0.1f; // 0.9 to 1.0
    } else if (avgDeltaE < 2.0f) {
        quality = 0.8f + (2.0f - avgDeltaE) * 0.1f; // 0.8 to 0.9
    } else if (avgDeltaE < 5.0f) {
        quality = 0.5f + (5.0f - avgDeltaE) * 0.1f; // 0.5 to 0.8
    } else if (avgDeltaE < 10.0f) {
        quality = 0.2f + (10.0f - avgDeltaE) * 0.06f; // 0.2 to 0.5
    } else {
        quality = std::max(0.0f, 0.2f - (avgDeltaE - 10.0f) * 0.02f); // < 0.2
    }

    LOG_CAL_INFO("Calibration validation: Avg Delta E = %.2f, Quality = %.3f", avgDeltaE, quality);

    return quality;
}

// ============================================================================
// COLOR SPACE CONVERSIONS
// ============================================================================

AdvancedTCS3430Calibration::CalibratedData AdvancedTCS3430Calibration::rawToXYZ(const RawSensorData& raw) {
    CalibratedData result = {0};

    if (!calibrationMatrix.valid) {
        LOG_CAL_WARN("Calibration matrix not valid, using raw values");
        result.x = raw.r;
        result.y = raw.g;
        result.z = raw.b;
        result.confidence = 0.0f;
        return result;
    }

    // Apply black point subtraction if available
    float r = raw.r, g = raw.g, b = raw.b;
    if (hasBlackPoint) {
        r = std::max(0.0f, r - blackPoint.r);
        g = std::max(0.0f, g - blackPoint.g);
        b = std::max(0.0f, b - blackPoint.b);
    }

    // Create input vector [R, G, B, 1] for matrix multiplication
    float input[4] = {r, g, b, 1.0f};
    float output[4] = {0};

    // Apply calibration matrix: output = matrix * input
    for (int i = 0; i < 4; i++) {
        for (int j = 0; j < 4; j++) {
            output[i] += calibrationMatrix.matrix[i * 4 + j] * input[j];
        }
    }

    // Extract XYZ coordinates
    result.x = output[0];
    result.y = output[1];
    result.z = output[2];

    // Apply dynamic IR compensation
    float compensationFactor = applyDynamicIRCompensation(raw, result.x, result.y, result.z);

    // Convert XYZ to sRGB
    xyzToSRGB(result.x, result.y, result.z, result.r_srgb, result.g_srgb, result.b_srgb);

    // Calculate confidence based on sensor saturation and calibration quality
    result.confidence = calibrationMatrix.quality;
    if (isSensorSaturated(raw)) {
        result.confidence *= 0.5f; // Reduce confidence for saturated readings
    }

    // Calculate Delta E from white point if available
    if (hasWhitePoint) {
        CalibratedData whiteXYZ = rawToXYZ(whitePoint);
        float white_L, white_a, white_b;
        float sample_L, sample_a, sample_b;

        srgbToLab(255, 255, 255, white_L, white_a, white_b); // Reference white
        srgbToLab(result.r_srgb, result.g_srgb, result.b_srgb, sample_L, sample_a, sample_b);

        result.deltaE = calculateDeltaE2000(white_L, white_a, white_b, sample_L, sample_a, sample_b);
    }

    return result;
}

void AdvancedTCS3430Calibration::xyzToSRGB(float x, float y, float z, uint8_t& r, uint8_t& g, uint8_t& b) {
    // XYZ to linear RGB transformation matrix (sRGB/D65)
    float linear_r = 3.2404542f * x - 1.5371385f * y - 0.4985314f * z;
    float linear_g = -0.9692660f * x + 1.8760108f * y + 0.0415560f * z;
    float linear_b = 0.0556434f * x - 0.2040259f * y + 1.0572252f * z;

    // Apply gamma correction
    auto gammaCorrect = [](float linear) -> float {
        if (linear <= 0.0031308f) {
            return 12.92f * linear;
        } else {
            return 1.055f * pow(linear, 1.0f/2.4f) - 0.055f;
        }
    };

    float srgb_r = gammaCorrect(linear_r);
    float srgb_g = gammaCorrect(linear_g);
    float srgb_b = gammaCorrect(linear_b);

    // Clamp to [0, 1] and convert to 8-bit
    r = (uint8_t)(std::max(0.0f, std::min(1.0f, srgb_r)) * 255.0f);
    g = (uint8_t)(std::max(0.0f, std::min(1.0f, srgb_g)) * 255.0f);
    b = (uint8_t)(std::max(0.0f, std::min(1.0f, srgb_b)) * 255.0f);
}

void AdvancedTCS3430Calibration::srgbToLab(uint8_t r, uint8_t g, uint8_t b,
                                          float& L, float& a, float& b_lab) {
    // Convert sRGB to linear RGB
    auto removeGamma = [](float srgb) -> float {
        if (srgb <= 0.04045f) {
            return srgb / 12.92f;
        } else {
            return pow((srgb + 0.055f) / 1.055f, 2.4f);
        }
    };

    float linear_r = removeGamma(r / 255.0f);
    float linear_g = removeGamma(g / 255.0f);
    float linear_b = removeGamma(b / 255.0f);

    // Convert linear RGB to XYZ (sRGB matrix)
    float X = 0.4124564f * linear_r + 0.3575761f * linear_g + 0.1804375f * linear_b;
    float Y = 0.2126729f * linear_r + 0.7151522f * linear_g + 0.0721750f * linear_b;
    float Z = 0.0193339f * linear_r + 0.1191920f * linear_g + 0.9503041f * linear_b;

    // Normalize by D65 white point
    X /= 0.95047f;
    Y /= 1.00000f;
    Z /= 1.08883f;

    // Convert XYZ to LAB
    auto f = [](float t) -> float {
        const float delta = 6.0f / 29.0f;
        if (t > delta * delta * delta) {
            return pow(t, 1.0f/3.0f);
        } else {
            return (t / (3.0f * delta * delta)) + (4.0f / 29.0f);
        }
    };

    float fx = f(X);
    float fy = f(Y);
    float fz = f(Z);

    L = 116.0f * fy - 16.0f;
    a = 500.0f * (fx - fy);
    b_lab = 200.0f * (fy - fz);
}

float AdvancedTCS3430Calibration::calculateDeltaE2000(float L1, float a1, float b1,
                                                     float L2, float a2, float b2) {
    // Simplified Delta E 2000 calculation
    // For full implementation, this would include weighting functions and rotation terms

    float deltaL = L1 - L2;
    float deltaA = a1 - a2;
    float deltaB = b1 - b2;

    // Calculate chroma values
    float C1 = sqrt(a1 * a1 + b1 * b1);
    float C2 = sqrt(a2 * a2 + b2 * b2);
    float deltaC = C1 - C2;

    // Calculate hue difference (simplified)
    float deltaH_squared = deltaA * deltaA + deltaB * deltaB - deltaC * deltaC;
    float deltaH = (deltaH_squared > 0) ? sqrt(deltaH_squared) : 0.0f;

    // Weighting factors (simplified - full DE2000 has complex weighting)
    float kL = 1.0f, kC = 1.0f, kH = 1.0f;
    float SL = 1.0f, SC = 1.0f + 0.045f * (C1 + C2) / 2.0f, SH = 1.0f + 0.015f * (C1 + C2) / 2.0f;

    // Calculate Delta E 2000
    float deltaE = sqrt(
        pow(deltaL / (kL * SL), 2) +
        pow(deltaC / (kC * SC), 2) +
        pow(deltaH / (kH * SH), 2)
    );

    return deltaE;
}

// ============================================================================
// MATRIX OPERATIONS AND HELPER METHODS
// ============================================================================

bool AdvancedTCS3430Calibration::solveLinearSystem(float* A, float* b, float* x,
                                                   int n, int m, float lambda) {
    // Solve A * x = b using Gaussian elimination with partial pivoting
    // A is n x n matrix, b is n x m matrix, x is n x m solution matrix

    // Create augmented matrix [A | b]
    float* augmented = new float[n * (n + m)];

    // Copy A and b into augmented matrix
    for (int i = 0; i < n; i++) {
        for (int j = 0; j < n; j++) {
            augmented[i * (n + m) + j] = A[i * n + j];
        }
        for (int j = 0; j < m; j++) {
            augmented[i * (n + m) + n + j] = b[i * m + j];
        }
    }

    // Forward elimination with partial pivoting
    for (int k = 0; k < n; k++) {
        // Find pivot
        int maxRow = k;
        for (int i = k + 1; i < n; i++) {
            if (abs(augmented[i * (n + m) + k]) > abs(augmented[maxRow * (n + m) + k])) {
                maxRow = i;
            }
        }

        // Swap rows if needed
        if (maxRow != k) {
            for (int j = 0; j < n + m; j++) {
                float temp = augmented[k * (n + m) + j];
                augmented[k * (n + m) + j] = augmented[maxRow * (n + m) + j];
                augmented[maxRow * (n + m) + j] = temp;
            }
        }

        // Check for singular matrix
        if (abs(augmented[k * (n + m) + k]) < 1e-10) {
            LOG_CAL_ERROR("Singular matrix detected during linear system solve");
            delete[] augmented;
            return false;
        }

        // Eliminate column
        for (int i = k + 1; i < n; i++) {
            float factor = augmented[i * (n + m) + k] / augmented[k * (n + m) + k];
            for (int j = k; j < n + m; j++) {
                augmented[i * (n + m) + j] -= factor * augmented[k * (n + m) + j];
            }
        }
    }

    // Back substitution
    for (int col = 0; col < m; col++) {
        for (int i = n - 1; i >= 0; i--) {
            x[i * m + col] = augmented[i * (n + m) + n + col];
            for (int j = i + 1; j < n; j++) {
                x[i * m + col] -= augmented[i * (n + m) + j] * x[j * m + col];
            }
            x[i * m + col] /= augmented[i * (n + m) + i];
        }
    }

    delete[] augmented;
    return true;
}

void AdvancedTCS3430Calibration::matrixMultiply(const float* A, const float* B, float* C,
                                               int rows_A, int cols_A, int cols_B) {
    // C = A^T * B (A is cols_A x rows_A, B is rows_A x cols_B, C is cols_A x cols_B)
    for (int i = 0; i < cols_A; i++) {
        for (int j = 0; j < cols_B; j++) {
            C[i * cols_B + j] = 0.0f;
            for (int k = 0; k < rows_A; k++) {
                C[i * cols_B + j] += A[k * cols_A + i] * B[k * cols_B + j]; // A^T * B
            }
        }
    }
}

bool AdvancedTCS3430Calibration::isSensorSaturated(const RawSensorData& raw) {
    const uint16_t saturationThreshold = 65000; // Close to 16-bit max

    return (raw.r >= saturationThreshold ||
            raw.g >= saturationThreshold ||
            raw.b >= saturationThreshold ||
            raw.c >= saturationThreshold);
}

float AdvancedTCS3430Calibration::getCalibrationQuality() {
    return calibrationMatrix.quality;
}

void AdvancedTCS3430Calibration::logCalibration(const char* level, const char* format, ...) {
    char buffer[256];
    va_list args;
    va_start(args, format);
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);

    if (strcmp(level, "ERROR") == 0) {
        LOG_CAL_ERROR("%s", buffer);
    } else if (strcmp(level, "WARN") == 0) {
        LOG_CAL_WARN("%s", buffer);
    } else if (strcmp(level, "INFO") == 0) {
        LOG_CAL_INFO("%s", buffer);
    } else {
        LOG_CAL_DEBUG("%s", buffer);
    }
}

// ============================================================================
// MISSING METHOD IMPLEMENTATIONS
// ============================================================================

bool AdvancedTCS3430Calibration::saveCalibrationData() {
    if (!preferences.begin("adv_cal", false)) {
        LOG_CAL_ERROR("Failed to open NVS for saving");
        return false;
    }

    // Save calibration matrix
    preferences.putBytes("cal_matrix", calibrationMatrix.matrix, sizeof(calibrationMatrix.matrix));
    preferences.putFloat("cal_quality", calibrationMatrix.quality);
    preferences.putBool("cal_valid", calibrationMatrix.valid);

    // Save calibration patches count
    preferences.putInt("num_patches", numPatches);

    preferences.end();
    LOG_CAL_INFO("Calibration data saved to NVS");
    return true;
}

bool AdvancedTCS3430Calibration::loadCalibrationData() {
    if (!preferences.begin("adv_cal", true)) {
        LOG_CAL_WARN("No existing calibration data found");
        return false;
    }

    // Load calibration matrix
    size_t matrixSize = preferences.getBytesLength("cal_matrix");
    if (matrixSize == sizeof(calibrationMatrix.matrix)) {
        preferences.getBytes("cal_matrix", calibrationMatrix.matrix, matrixSize);
        calibrationMatrix.quality = preferences.getFloat("cal_quality", 0.0f);
        calibrationMatrix.valid = preferences.getBool("cal_valid", false);

        numPatches = preferences.getInt("num_patches", 0);

        preferences.end();
        LOG_CAL_INFO("Calibration data loaded from NVS");
        return true;
    }

    preferences.end();
    LOG_CAL_WARN("Invalid calibration data in NVS");
    return false;
}

void AdvancedTCS3430Calibration::getCalibrationStatus(JsonDocument& doc) {
    doc["initialized"] = initialized;
    doc["matrixValid"] = calibrationMatrix.valid;
    doc["quality"] = calibrationMatrix.quality;
    doc["numPatches"] = numPatches;
    doc["pidActive"] = true; // Assume PID is always active when initialized
    doc["currentLEDBrightness"] = currentLEDBrightness;
    doc["targetSensorValue"] = TARGET_SENSOR_VALUE;

    if (initialized) {
        auto sensorData = readSingleSample();
        doc["currentSensorValue"] = std::max({(float)sensorData.r, (float)sensorData.g, (float)sensorData.b});
        doc["irContamination"] = calculateIRContamination(sensorData);
        doc["saturation"] = isSensorSaturated(sensorData);
    }
}

void AdvancedTCS3430Calibration::getSensorDiagnostics(JsonDocument& doc) {
    if (!initialized) {
        doc["error"] = "System not initialized";
        return;
    }

    auto sensorData = readSingleSample();

    doc["sensor"]["r"] = sensorData.r;
    doc["sensor"]["g"] = sensorData.g;
    doc["sensor"]["b"] = sensorData.b;
    doc["sensor"]["c"] = sensorData.c;
    doc["sensor"]["ir1"] = sensorData.ir1;
    doc["sensor"]["ir2"] = sensorData.ir2;
    doc["sensor"]["temperature"] = sensorData.temperature;
    doc["sensor"]["timestamp"] = sensorData.timestamp;

    doc["analysis"]["irContamination"] = calculateIRContamination(sensorData);
    doc["analysis"]["saturation"] = isSensorSaturated(sensorData);
    doc["analysis"]["maxValue"] = std::max({(float)sensorData.r, (float)sensorData.g, (float)sensorData.b});

    doc["calibration"]["valid"] = calibrationMatrix.valid;
    doc["calibration"]["quality"] = calibrationMatrix.quality;
    doc["calibration"]["patches"] = numPatches;

    doc["led"]["brightness"] = currentLEDBrightness;
    doc["led"]["pidActive"] = true;
}
