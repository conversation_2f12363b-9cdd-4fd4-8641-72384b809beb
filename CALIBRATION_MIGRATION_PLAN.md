# TCS3430 Calibration System Migration Plan

## 🎯 Strategy Decision

**PRIMARY SYSTEM:** `TCS3430Calibration` 
**LEGACY SYSTEM:** `MatrixCalibration` (to be deprecated)

## 📊 Current State Analysis

### TCS3430Calibration (Modern - Primary)
✅ **Advantages:**
- 4x4 transformation matrices (vs 3x4 in legacy)
- Dual-matrix system for low-IR and high-IR illumination
- Smooth-step IR blending for mixed lighting conditions
- Auto-zero calibration for dark offset compensation
- Factory defaults from PROGMEM
- AN000571 methodology compliance (industry standard)
- Better structured code with proper error handling

❌ **Missing Features:**
- LAB color space Delta E calculation (currently simple RGB)
- Pre-defined color reference sets (ColorChecker, Dulux)

### MatrixCalibration (Legacy - To Deprecate)
✅ **Valuable Features to Migrate:**
- LAB color space Delta E calculation (more perceptually accurate)
- ColorChecker and Dulux color references
- Least squares matrix solving implementation

❌ **Limitations:**
- 3x4 matrices only (less sophisticated)
- No IR compensation
- No dual-matrix support

## 🔄 Migration Phases

### Phase 1: Enhance TCS3430Calibration ⭐ **PRIORITY**

#### 1.1 Migrate LAB Color Space Delta E
- [ ] Add `srgbToLab()` method to TCS3430Calibration
- [ ] Add `applyGammaCorrection()` and `removeGammaCorrection()` methods
- [ ] Replace simple RGB Delta E with LAB-based calculation
- [ ] Update `calculateDeltaE()` to use CIE76 formula

#### 1.2 Add Pre-defined Color References
- [ ] Add `loadColorCheckerReferences()` method
- [ ] Add `loadDuluxColorReferences()` method
- [ ] Migrate color reference data from MatrixCalibration

#### 1.3 Verify Matrix Computation
- [ ] Ensure `computeMatrix()` implementation exists
- [ ] Add least squares solving if needed
- [ ] Test matrix computation accuracy

### Phase 2: Implement API Handlers

#### 2.1 Replace Stub Functions
Current stub functions in main.cpp:
```cpp
void handleTCS3430CalibrationStatus() {
  server.send(200, "application/json", "{\"error\":\"TCS3430 calibration not implemented\"}");
}
```

- [ ] `handleTCS3430CalibrationStatus()` - Return calibration state and statistics
- [ ] `handleTCS3430CalibrationAutoZero()` - Perform auto-zero calibration
- [ ] `handleTCS3430CalibrationSetMatrix()` - Set/import calibration matrix
- [ ] `handleTCS3430CalibrationGetDiagnostics()` - Return sensor diagnostics
- [ ] `handleTCS3430CalibrationExportData()` - Export calibration data

#### 2.2 API Endpoint Implementation
```cpp
// Example implementation structure:
void handleTCS3430CalibrationStatus() {
  if (tcs3430Calibration && tcs3430Calibration->isInitialized()) {
    JsonDocument doc;
    tcs3430Calibration->getCalibrationStatus(doc);
    String response;
    serializeJson(doc, response);
    server.send(200, "application/json", response);
  } else {
    server.send(500, "application/json", "{\"error\":\"Calibration system not initialized\"}");
  }
}
```

### Phase 3: Update React Components

#### 3.1 Update MatrixCalibrationPanel
- [ ] Change API endpoints from `/matrix-calibration/*` to `/tcs3430-calibration/*`
- [ ] Add support for dual-matrix calibration UI
- [ ] Add IR threshold configuration controls
- [ ] Update calibration workflow for TCS3430 system

#### 3.2 Add New UI Components
- [ ] Auto-zero calibration controls
- [ ] Factory defaults reset option
- [ ] IR blending threshold settings
- [ ] Enhanced calibration quality display (Delta E statistics)

### Phase 4: Deprecation and Cleanup

#### 4.1 Remove Legacy System
- [ ] Remove `MatrixCalibration* matrixCalibration` from main.cpp
- [ ] Remove legacy API endpoints (`/matrix-calibration/*`)
- [ ] Remove `#include "matrix_calibration.h"` from main.cpp

#### 4.2 File Cleanup
- [ ] Delete `src/matrix_calibration.cpp`
- [ ] Delete `src/matrix_calibration.h`
- [ ] Update any remaining references

#### 4.3 Documentation Update
- [ ] Update API documentation
- [ ] Update calibration workflow documentation
- [ ] Add migration notes for existing users

## 🧪 Testing Strategy

### Unit Tests
- [ ] Test LAB color space conversion accuracy
- [ ] Test Delta E calculation against known values
- [ ] Test matrix computation with known calibration points
- [ ] Test dual-matrix blending logic

### Integration Tests
- [ ] Test full calibration workflow via API
- [ ] Test calibration data persistence (NVS)
- [ ] Test factory defaults loading
- [ ] Test auto-zero calibration sequence

### Validation Tests
- [ ] Compare Delta E accuracy: RGB vs LAB methods
- [ ] Validate ColorChecker calibration results
- [ ] Test calibration quality with different lighting conditions
- [ ] Verify IR compensation effectiveness

## 📋 Implementation Checklist

### Immediate Actions (Phase 1)
- [x] Add LAB color space method declarations to TCS3430Calibration.h
- [x] Add color reference loading method declarations
- [ ] Implement LAB color space conversion in TCS3430Calibration.cpp
- [ ] Implement color reference loading methods
- [ ] Update calculateDeltaE to use LAB color space

### Next Steps (Phase 2)
- [ ] Implement all TCS3430 calibration API handlers
- [ ] Test API endpoints with Postman/curl
- [ ] Update React components to use new endpoints

### Future (Phases 3-4)
- [ ] Complete React UI migration
- [ ] Remove legacy system
- [ ] Performance optimization and testing

## 🎯 Success Criteria

1. **Functional Parity:** TCS3430Calibration provides all features of MatrixCalibration
2. **Improved Accuracy:** LAB-based Delta E provides better color difference assessment
3. **Enhanced Features:** Dual-matrix and IR compensation improve calibration quality
4. **Clean Architecture:** Single, well-structured calibration system
5. **API Completeness:** All calibration features accessible via web interface
6. **Documentation:** Clear migration path and usage instructions

## 📝 Notes

- **Backward Compatibility:** Existing calibration data should be preserved during migration
- **Performance:** New system should maintain or improve calibration speed
- **Memory Usage:** Monitor RAM usage with enhanced features
- **Error Handling:** Robust error handling for all calibration operations
