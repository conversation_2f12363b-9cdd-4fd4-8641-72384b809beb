# Security Cleanup Guide for ESP32 Color Matcher

## 🚨 Critical: Remove Sensitive Data Before GitHub Upload

### **Sensitive Data Found:**

1. **WiFi Credentials** (config.h):
   - SSID: "Wifi 6"
   - Password: "Scrofani1985"

2. **Hardcoded IP Addresses**: *************

3. **Google Apps Script URLs** with script IDs

4. **Personal file paths** in VS Code configuration

## 🛠️ Cleanup Strategy

### **Option 1: Manual Cleanup (Recommended)**

#### Step 1: Create Template Configuration Files

```bash
# 1. Create config template
cp config.h config.h.template
cp src/config.h src/config.h.template

# 2. Remove sensitive data from templates
# 3. Add templates to git, ignore actual config files
```

#### Step 2: Update .gitignore
```gitignore
# Sensitive configuration files
config.h
src/config.h
secrets.h
wifi_credentials.h

# Environment-specific files
.env.local
.env.production
```

#### Step 3: Replace Sensitive Values

**In config.h and src/config.h:**
```cpp
// WiFi Configuration - UPDATE THESE WITH YOUR CREDENTIALS
#define WIFI_SSID "YOUR_WIFI_SSID"
#define WIFI_PASSWORD "YOUR_WIFI_PASSWORD"

// Google Apps Script Configuration
#define GOOGLE_SCRIPT_URL "https://script.google.com/macros/s/YOUR_SCRIPT_ID/exec"
```

**In test scripts and documentation:**
```bash
# Replace ************* with:
ESP32_IP = "*************"  # Generic example IP
DEVICE_IP=*************     # In documentation
```

### **Option 2: Git Filter-Branch (Advanced)**

If you've already committed sensitive data:

```bash
# WARNING: This rewrites Git history - use with caution

# Remove specific file from all history
git filter-branch --force --index-filter \
  'git rm --cached --ignore-unmatch config.h' \
  --prune-empty --tag-name-filter cat -- --all

# Remove specific strings from all files
git filter-branch --force --tree-filter \
  'find . -type f -exec sed -i "s/Scrofani1985/YOUR_WIFI_PASSWORD/g" {} \;' \
  --prune-empty --tag-name-filter cat -- --all

# Clean up
git for-each-ref --format='delete %(refname)' refs/original | git update-ref --stdin
git reflog expire --expire=now --all
git gc --prune=now --aggressive
```

### **Option 3: BFG Repo-Cleaner (Easiest)**

```bash
# Install BFG (requires Java)
# Download from: https://rtyley.github.io/bfg-repo-cleaner/

# Create passwords.txt with sensitive strings
echo "Scrofani1985" > passwords.txt
echo "*************" >> passwords.txt

# Clean repository
java -jar bfg.jar --replace-text passwords.txt .git
git reflog expire --expire=now --all
git gc --prune=now --aggressive

# Clean up
rm passwords.txt
```

## 📋 Cleanup Checklist

### **Files to Clean:**

- [ ] `config.h` (root) - WiFi credentials
- [ ] `src/config.h` - WiFi credentials, Google Script URL
- [ ] `test_esp32.ps1` - IP address
- [ ] `simple_vivid_white_test.py` - IP address
- [ ] `simple_test.ps1` - IP address
- [ ] `README.md` - IP address references
- [ ] `mock_esp32_server.py` - IP references
- [ ] `color_matcher_server.py` - IP references
- [ ] `context.json` - Device IP
- [ ] `quick_test.sh` - Default IP
- [ ] `quick_test.ps1` - Default IP
- [ ] `main_snapshot.cpp` - WiFi credentials
- [ ] `test_black_calibration.py` - IP address

### **VS Code Configuration:**
- [ ] Add `.vscode/` to .gitignore (contains personal paths)
- [ ] Remove or sanitize `c_cpp_properties.json`

## 🔧 Implementation Commands

### **1. Create Configuration Templates**

```bash
# Create template files
sed 's/Wifi 6/YOUR_WIFI_SSID/g; s/Scrofani1985/YOUR_WIFI_PASSWORD/g' config.h > config.h.template
sed 's/Wifi 6/YOUR_WIFI_SSID/g; s/Scrofani1985/YOUR_WIFI_PASSWORD/g' src/config.h > src/config.h.template

# Replace Google Script URLs
sed -i 's/AKfycb[a-zA-Z0-9_-]*/YOUR_SCRIPT_ID/g' *.template
```

### **2. Update IP Addresses**

```bash
# Replace hardcoded IPs with generic examples
find . -name "*.py" -exec sed -i 's/192\.168\.0\.152/*************/g' {} \;
find . -name "*.ps1" -exec sed -i 's/192\.168\.0\.152/*************/g' {} \;
find . -name "*.md" -exec sed -i 's/192\.168\.0\.152/*************/g' {} \;
find . -name "*.json" -exec sed -i 's/192\.168\.0\.152/*************/g' {} \;
```

### **3. Update .gitignore**

```bash
# Add sensitive files to .gitignore
cat >> .gitignore << EOF

# Sensitive configuration files
config.h
src/config.h
secrets.h
wifi_credentials.h

# VS Code personal settings
.vscode/c_cpp_properties.json

# Environment files
.env.local
.env.production
EOF
```

## 🚀 Safe Upload Process

### **1. Initialize Clean Repository**

```bash
# Initialize git (if not already done)
git init

# Add .gitignore first
git add .gitignore
git commit -m "Add .gitignore with security exclusions"

# Add template files
git add config.h.template src/config.h.template
git commit -m "Add configuration templates"

# Add cleaned files
git add .
git commit -m "Initial commit with sanitized code"
```

### **2. Create GitHub Repository**

```bash
# Option A: Using GitHub CLI (if installed)
gh repo create esp32-color-matching --public --description "ESP32-based color measurement device with TCS3430 sensor"

# Option B: Manual creation
# 1. Go to https://github.com/new
# 2. Create repository: esp32-color-matching
# 3. Don't initialize with README (you already have one)
```

### **3. Push to GitHub**

```bash
# Add remote origin
git remote add origin https://github.com/YOUR_USERNAME/esp32-color-matching.git

# Push to GitHub
git branch -M main
git push -u origin main
```

## ⚠️ Important Notes

1. **Never commit real credentials** - always use templates
2. **Review each file** before committing
3. **Test with template files** to ensure functionality
4. **Document setup process** for other developers
5. **Consider using environment variables** for configuration

## 📖 User Setup Instructions

Add this to your README:

```markdown
## 🔧 Configuration Setup

1. Copy configuration templates:
   ```bash
   cp config.h.template config.h
   cp src/config.h.template src/config.h
   ```

2. Edit config.h files with your credentials:
   - Update WiFi SSID and password
   - Set your device IP address
   - Configure Google Apps Script URL (if using)

3. Build and deploy as normal
```
