# ESP32 Color Matcher Setup Guide

## 🔧 Initial Configuration

### **Step 1: Configure WiFi Credentials**

The configuration files contain placeholder values that need to be updated with your actual network credentials.

#### **Update config.h (Root Directory)**
```cpp
// WiFi Configuration - UPDATE THESE WITH YOUR CREDENTIALS
#define WIFI_SSID "YOUR_WIFI_SSID"        // Replace with your WiFi network name
#define WIFI_PASSWORD "YOUR_WIFI_PASSWORD" // Replace with your WiFi password
```

#### **Update src/config.h (Firmware Configuration)**
```cpp
// WiFi Configuration
#define WIFI_SSID "YOUR_WIFI_SSID"        // Replace with your WiFi network name
#define WIFI_PASSWORD "YOUR_WIFI_PASSWORD" // Replace with your WiFi password
```

### **Step 2: Configure Device IP Address**

Update the IP address to match your network configuration:

#### **In src/config.h:**
```cpp
#define STATIC_IP_ADDRESS "*************"  // Update to your preferred IP
```

#### **In context.json:**
```json
{
  "project": {
    "deviceIP": "*************"  // Update to match your device IP
  }
}
```

### **Step 3: Google Apps Script Configuration (Optional)**

If you want to use cloud-based color matching:

1. **Deploy the Google Apps Script** (see `GOOGLE_APPS_SCRIPT_DEPLOYMENT.md`)
2. **Update the script URL** in `src/config.h`:
```cpp
#define GOOGLE_SCRIPT_URL "https://script.google.com/macros/s/YOUR_ACTUAL_SCRIPT_ID/exec"
```

## 🚀 Quick Setup Commands

### **For Windows (PowerShell):**
```powershell
# 1. Clone the repository
git clone https://github.com/elliotsnd/esp32-color-matching.git
cd esp32-color-matching

# 2. Install dependencies
npm install

# 3. Edit configuration files
notepad config.h
notepad src/config.h

# 4. Build and deploy
.\scripts\deploy.ps1
```

### **For Linux/macOS:**
```bash
# 1. Clone the repository
git clone https://github.com/elliotsnd/esp32-color-matching.git
cd esp32-color-matching

# 2. Install dependencies
npm install

# 3. Edit configuration files
nano config.h
nano src/config.h

# 4. Build and deploy
./scripts/deploy.sh  # If available, or use PlatformIO commands
```

## 🔌 Hardware Setup

### **Required Components:**
- ESP32-S3 DevKit-C-1 (or compatible)
- TCS3430 Color Sensor
- LED for illumination
- Jumper wires

### **Wiring Diagram:**
```
ESP32-S3          TCS3430 Sensor
--------          ---------------
3.3V       -----> VCC
GND        -----> GND
GPIO21     -----> SDA (I2C Data)
GPIO22     -----> SCL (I2C Clock)

ESP32-S3          LED
--------          ---
GPIO5      -----> LED+ (through resistor)
GND        -----> LED-
```

## 📱 Network Configuration

### **Option 1: DHCP (Automatic IP)**
Set `USE_STATIC_IP false` in `src/config.h`:
```cpp
#define USE_STATIC_IP false
```

### **Option 2: Static IP (Recommended)**
Set `USE_STATIC_IP true` and configure your network:
```cpp
#define USE_STATIC_IP true
#define STATIC_IP_ADDRESS "*************"
#define STATIC_GATEWAY "***********"
#define STATIC_SUBNET "*************"
#define STATIC_DNS1 "*******"
#define STATIC_DNS2 "*******"
```

## 🧪 Testing Your Setup

### **1. Basic Connectivity Test**
```bash
# Test if ESP32 is reachable
curl http://*************/status

# Or use PowerShell
Invoke-RestMethod -Uri "http://*************/status"
```

### **2. Web Interface Test**
Open your browser to: `http://*************`

### **3. Sensor Test**
Use the provided test scripts:
```bash
# PowerShell
.\quick_test.ps1 *************

# Python
python test_calibration.py
```

## 🔧 Troubleshooting

### **WiFi Connection Issues**
1. **Check credentials** in both config.h files
2. **Verify network compatibility** (2.4GHz required for ESP32)
3. **Check signal strength** at device location
4. **Monitor serial output** for connection errors

### **IP Address Issues**
1. **Check router DHCP range** if using static IP
2. **Verify no IP conflicts** with other devices
3. **Test with DHCP first** if static IP fails
4. **Check firewall settings** on your computer

### **Sensor Issues**
1. **Verify I2C wiring** (SDA/SCL connections)
2. **Check power supply** (3.3V stable)
3. **Test I2C communication** using serial monitor
4. **Ensure proper sensor mounting** for light exposure

### **Build Issues**
1. **Install PlatformIO** if not already installed
2. **Check library dependencies** in platformio.ini
3. **Verify ESP32 board selection** in PlatformIO
4. **Clear build cache** if compilation fails

## 📖 Additional Resources

- **Calibration Guide**: See calibration section in main README.md
- **API Documentation**: Check `/docs` folder for API endpoints
- **Google Apps Script**: See `GOOGLE_APPS_SCRIPT_DEPLOYMENT.md`
- **Development Workflow**: See `docs/DEVELOPMENT_WORKFLOW.md`

## 🆘 Getting Help

If you encounter issues:

1. **Check the logs** via serial monitor
2. **Review existing issues** on GitHub
3. **Create a new issue** with:
   - Your configuration (without credentials)
   - Error messages
   - Serial monitor output
   - Hardware setup details

## 🔒 Security Notes

- **Never commit real credentials** to version control
- **Use strong WiFi passwords**
- **Consider network isolation** for IoT devices
- **Regularly update firmware** for security patches
- **Monitor device access logs** if available

---

**Need help?** Check the troubleshooting section or create an issue on GitHub!
