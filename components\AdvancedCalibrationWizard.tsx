import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Progress } from './ui/progress';
import { Badge } from './ui/badge';
import { Alert, AlertDescription } from './ui/alert';
import { Loader2, CheckCircle, AlertTriangle, XCircle } from 'lucide-react';

// ============================================================================
// ADVANCED CALIBRATION WIZARD WITH REAL-TIME DELTA E
// Complete replacement for existing calibration UI
// Features: Real-time Delta E 2000, PID LED control, noise reduction visualization
// ============================================================================

interface CalibrationPatch {
  name: string;
  ref_x: number;
  ref_y: number;
  ref_z: number;
  ref_r: number;
  ref_g: number;
  ref_b: number;
  measured_r: number;
  measured_g: number;
  measured_b: number;
  deltaE: number;
  confidence: number;
  status: 'pending' | 'measuring' | 'complete' | 'error';
}

interface CalibrationStatus {
  isActive: boolean;
  currentPatch: number;
  totalPatches: number;
  averageDeltaE: number;
  quality: number;
  matrixValid: boolean;
  pidActive: boolean;
  currentLEDBrightness: number;
  targetSensorValue: number;
  currentSensorValue: number;
}

interface SensorMetrics {
  r: number;
  g: number;
  b: number;
  c: number;
  ir1: number;
  ir2: number;
  irContamination: number;
  saturation: boolean;
  timestamp: number;
}

const COLORCHECKER_PATCHES: Omit<CalibrationPatch, 'measured_r' | 'measured_g' | 'measured_b' | 'deltaE' | 'confidence' | 'status'>[] = [
  { name: 'Dark Skin', ref_x: 10.1, ref_y: 9.0, ref_z: 5.1, ref_r: 115, ref_g: 82, ref_b: 68 },
  { name: 'Light Skin', ref_x: 35.8, ref_y: 35.6, ref_z: 29.0, ref_r: 194, ref_g: 150, ref_b: 130 },
  { name: 'Blue Sky', ref_x: 19.3, ref_y: 20.1, ref_z: 42.1, ref_r: 98, ref_g: 122, ref_b: 157 },
  { name: 'Foliage', ref_x: 13.3, ref_y: 15.3, ref_z: 9.3, ref_r: 87, ref_g: 108, ref_b: 67 },
  { name: 'Blue Flower', ref_x: 24.3, ref_y: 22.9, ref_z: 49.7, ref_r: 133, ref_g: 128, ref_b: 177 },
  { name: 'Bluish Green', ref_x: 26.9, ref_y: 36.2, ref_z: 40.6, ref_r: 103, ref_g: 189, ref_b: 170 },
  { name: 'Orange', ref_x: 31.1, ref_y: 26.0, ref_z: 7.2, ref_r: 214, ref_g: 126, ref_b: 44 },
  { name: 'Purplish Blue', ref_x: 12.0, ref_y: 9.4, ref_z: 31.2, ref_r: 80, ref_g: 91, ref_b: 166 },
  { name: 'Moderate Red', ref_x: 19.8, ref_y: 12.0, ref_z: 7.9, ref_r: 193, ref_g: 90, ref_b: 99 },
  { name: 'Purple', ref_x: 6.6, ref_y: 4.1, ref_z: 11.5, ref_r: 94, ref_g: 60, ref_b: 108 },
  { name: 'Yellow Green', ref_x: 44.3, ref_y: 54.1, ref_z: 17.1, ref_r: 157, ref_g: 188, ref_b: 64 },
  { name: 'Orange Yellow', ref_x: 43.1, ref_y: 42.0, ref_z: 8.8, ref_r: 224, ref_g: 163, ref_b: 46 },
  { name: 'Blue', ref_x: 6.1, ref_y: 4.2, ref_z: 22.4, ref_r: 56, ref_g: 61, ref_b: 150 },
  { name: 'Green', ref_x: 23.4, ref_y: 37.1, ref_z: 13.9, ref_r: 70, ref_g: 148, ref_b: 73 },
  { name: 'Red', ref_x: 12.0, ref_y: 6.7, ref_z: 2.9, ref_r: 175, ref_g: 54, ref_b: 60 },
  { name: 'Yellow', ref_x: 59.1, ref_y: 62.7, ref_z: 13.4, ref_r: 231, ref_g: 199, ref_b: 31 },
  { name: 'Magenta', ref_x: 19.4, ref_y: 12.0, ref_z: 17.8, ref_r: 187, ref_g: 86, ref_b: 149 },
  { name: 'Cyan', ref_x: 19.8, ref_y: 25.4, ref_z: 47.6, ref_r: 8, ref_g: 133, ref_b: 161 },
  { name: 'White', ref_x: 90.0, ref_y: 90.0, ref_z: 90.0, ref_r: 243, ref_g: 243, ref_b: 242 },
  { name: 'Neutral 8', ref_x: 59.1, ref_y: 59.1, ref_z: 59.1, ref_r: 200, ref_g: 200, ref_b: 200 },
  { name: 'Neutral 6.5', ref_x: 36.2, ref_y: 36.2, ref_z: 36.2, ref_r: 160, ref_g: 160, ref_b: 160 },
  { name: 'Neutral 5', ref_x: 19.8, ref_y: 19.8, ref_z: 19.8, ref_r: 122, ref_g: 122, ref_b: 121 },
  { name: 'Neutral 3.5', ref_x: 9.0, ref_y: 9.0, ref_z: 9.0, ref_r: 85, ref_g: 85, ref_b: 85 },
  { name: 'Black', ref_x: 3.1, ref_y: 3.1, ref_z: 3.1, ref_r: 52, ref_g: 52, ref_b: 52 }
];

export const AdvancedCalibrationWizard: React.FC = () => {
  const [calibrationStatus, setCalibrationStatus] = useState<CalibrationStatus>({
    isActive: false,
    currentPatch: 0,
    totalPatches: 0,
    averageDeltaE: 0,
    quality: 0,
    matrixValid: false,
    pidActive: false,
    currentLEDBrightness: 128,
    targetSensorValue: 30000,
    currentSensorValue: 0
  });

  const [patches, setPatches] = useState<CalibrationPatch[]>([]);
  const [sensorMetrics, setSensorMetrics] = useState<SensorMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize ColorChecker patches
  useEffect(() => {
    const initialPatches: CalibrationPatch[] = COLORCHECKER_PATCHES.map(patch => ({
      ...patch,
      measured_r: 0,
      measured_g: 0,
      measured_b: 0,
      deltaE: 0,
      confidence: 0,
      status: 'pending'
    }));
    setPatches(initialPatches);
    setCalibrationStatus(prev => ({ ...prev, totalPatches: initialPatches.length }));
  }, []);

  // Real-time sensor metrics polling
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (calibrationStatus.isActive || calibrationStatus.pidActive) {
      interval = setInterval(async () => {
        try {
          const response = await fetch('/api/advanced-calibration/sensor-metrics');
          if (response.ok) {
            const metrics = await response.json();
            setSensorMetrics(metrics);
            
            // Update calibration status with current sensor value
            setCalibrationStatus(prev => ({
              ...prev,
              currentSensorValue: Math.max(metrics.r, metrics.g, metrics.b),
              currentLEDBrightness: metrics.ledBrightness || prev.currentLEDBrightness
            }));
          }
        } catch (err) {
          console.error('Failed to fetch sensor metrics:', err);
        }
      }, 1000); // Update every second
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [calibrationStatus.isActive, calibrationStatus.pidActive]);

  const startCalibration = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Initialize advanced calibration system
      const initResponse = await fetch('/api/advanced-calibration/initialize', {
        method: 'POST'
      });
      
      if (!initResponse.ok) {
        throw new Error('Failed to initialize calibration system');
      }

      // Enable PID LED control
      const pidResponse = await fetch('/api/advanced-calibration/enable-pid', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          target: calibrationStatus.targetSensorValue,
          kp: 2.0,
          ki: 0.1,
          kd: 0.05
        })
      });

      if (!pidResponse.ok) {
        throw new Error('Failed to enable PID control');
      }

      setCalibrationStatus(prev => ({
        ...prev,
        isActive: true,
        pidActive: true,
        currentPatch: 0
      }));

      // Reset all patches to pending
      setPatches(prev => prev.map(patch => ({
        ...patch,
        status: 'pending',
        deltaE: 0,
        confidence: 0
      })));

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  }, [calibrationStatus.targetSensorValue]);

  const measurePatch = useCallback(async (patchIndex: number) => {
    if (patchIndex >= patches.length) return;

    setPatches(prev => prev.map((patch, idx) => 
      idx === patchIndex ? { ...patch, status: 'measuring' } : patch
    ));

    try {
      // Take averaged measurement with noise reduction
      const measureResponse = await fetch('/api/advanced-calibration/measure-patch', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          patchIndex,
          numSamples: 10,
          stabilizationDelay: 50,
          useIRCompensation: true
        })
      });

      if (!measureResponse.ok) {
        throw new Error('Failed to measure patch');
      }

      const measurement = await measureResponse.json();

      // Update patch with measurement results
      setPatches(prev => prev.map((patch, idx) => 
        idx === patchIndex ? {
          ...patch,
          measured_r: measurement.r,
          measured_g: measurement.g,
          measured_b: measurement.b,
          deltaE: measurement.deltaE,
          confidence: measurement.confidence,
          status: 'complete'
        } : patch
      ));

      // Move to next patch
      setCalibrationStatus(prev => ({
        ...prev,
        currentPatch: patchIndex + 1
      }));

    } catch (err) {
      setPatches(prev => prev.map((patch, idx) => 
        idx === patchIndex ? { ...patch, status: 'error' } : patch
      ));
      setError(`Failed to measure patch ${patchIndex + 1}: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  }, [patches.length]);

  const computeMatrix = useCallback(async () => {
    setIsLoading(true);
    
    try {
      const response = await fetch('/api/advanced-calibration/compute-matrix', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          lambda: 0.01, // Tikhonov regularization parameter
          patches: patches.filter(p => p.status === 'complete')
        })
      });

      if (!response.ok) {
        throw new Error('Failed to compute calibration matrix');
      }

      const result = await response.json();
      
      setCalibrationStatus(prev => ({
        ...prev,
        matrixValid: result.success,
        quality: result.quality,
        averageDeltaE: result.averageDeltaE,
        isActive: false
      }));

      if (result.success) {
        // Validate all patches with new matrix
        await validateCalibration();
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Matrix computation failed');
    } finally {
      setIsLoading(false);
    }
  }, [patches]);

  const validateCalibration = useCallback(async () => {
    try {
      const response = await fetch('/api/advanced-calibration/validate');
      if (response.ok) {
        const validation = await response.json();
        
        // Update patches with validation results
        setPatches(prev => prev.map((patch, idx) => ({
          ...patch,
          deltaE: validation.patches[idx]?.deltaE || patch.deltaE,
          confidence: validation.patches[idx]?.confidence || patch.confidence
        })));

        setCalibrationStatus(prev => ({
          ...prev,
          averageDeltaE: validation.averageDeltaE,
          quality: validation.quality
        }));
      }
    } catch (err) {
      console.error('Validation failed:', err);
    }
  }, []);

  const getDeltaEColor = (deltaE: number): string => {
    if (deltaE < 1) return 'text-green-600';
    if (deltaE < 2) return 'text-blue-600';
    if (deltaE < 5) return 'text-yellow-600';
    if (deltaE < 10) return 'text-orange-600';
    return 'text-red-600';
  };

  const getDeltaELabel = (deltaE: number): string => {
    if (deltaE < 1) return 'Excellent';
    if (deltaE < 2) return 'Very Good';
    if (deltaE < 5) return 'Acceptable';
    if (deltaE < 10) return 'Poor';
    return 'Very Poor';
  };

  const getQualityColor = (quality: number): string => {
    if (quality > 0.9) return 'text-green-600';
    if (quality > 0.8) return 'text-blue-600';
    if (quality > 0.5) return 'text-yellow-600';
    if (quality > 0.2) return 'text-orange-600';
    return 'text-red-600';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            Advanced TCS3430 Calibration Wizard
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {calibrationStatus.quality.toFixed(3)}
              </div>
              <div className="text-sm text-gray-600">Calibration Quality</div>
            </div>
            <div className="text-center">
              <div className={`text-2xl font-bold ${getDeltaEColor(calibrationStatus.averageDeltaE)}`}>
                {calibrationStatus.averageDeltaE.toFixed(2)}
              </div>
              <div className="text-sm text-gray-600">Average ΔE 2000</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {patches.filter(p => p.status === 'complete').length}/{patches.length}
              </div>
              <div className="text-sm text-gray-600">Patches Complete</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Real-time Sensor Metrics */}
      {sensorMetrics && (
        <Card>
          <CardHeader>
            <CardTitle>Real-time Sensor Metrics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-lg font-semibold text-red-600">{sensorMetrics.r}</div>
                <div className="text-sm text-gray-600">Red</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-green-600">{sensorMetrics.g}</div>
                <div className="text-sm text-gray-600">Green</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-blue-600">{sensorMetrics.b}</div>
                <div className="text-sm text-gray-600">Blue</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-gray-600">{sensorMetrics.c}</div>
                <div className="text-sm text-gray-600">Clear</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-purple-600">{sensorMetrics.ir1}</div>
                <div className="text-sm text-gray-600">IR1</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-purple-600">{sensorMetrics.ir2}</div>
                <div className="text-sm text-gray-600">IR2</div>
              </div>
              <div className="text-center">
                <div className={`text-lg font-semibold ${sensorMetrics.irContamination > 0.3 ? 'text-red-600' : 'text-green-600'}`}>
                  {(sensorMetrics.irContamination * 100).toFixed(1)}%
                </div>
                <div className="text-sm text-gray-600">IR Contamination</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-orange-600">
                  {calibrationStatus.currentLEDBrightness}
                </div>
                <div className="text-sm text-gray-600">LED Brightness</div>
              </div>
            </div>

            {sensorMetrics.saturation && (
              <Alert className="mt-4">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  Sensor saturation detected! Reduce LED brightness or integration time.
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      )}

      {/* PID Control Status */}
      {calibrationStatus.pidActive && (
        <Card>
          <CardHeader>
            <CardTitle>PID LED Control</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span>Target Sensor Value:</span>
                <span className="font-semibold">{calibrationStatus.targetSensorValue}</span>
              </div>
              <div className="flex justify-between items-center">
                <span>Current Sensor Value:</span>
                <span className={`font-semibold ${
                  Math.abs(calibrationStatus.currentSensorValue - calibrationStatus.targetSensorValue) < 5000
                    ? 'text-green-600' : 'text-orange-600'
                }`}>
                  {calibrationStatus.currentSensorValue}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span>LED Brightness:</span>
                <span className="font-semibold">{calibrationStatus.currentLEDBrightness}/255</span>
              </div>
              <Progress
                value={(calibrationStatus.currentSensorValue / calibrationStatus.targetSensorValue) * 100}
                className="w-full"
              />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Control Buttons */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex gap-4 flex-wrap">
            <Button
              onClick={startCalibration}
              disabled={isLoading || calibrationStatus.isActive}
              className="flex items-center gap-2"
            >
              {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <CheckCircle className="h-4 w-4" />}
              Start Advanced Calibration
            </Button>

            {calibrationStatus.isActive && calibrationStatus.currentPatch < patches.length && (
              <Button
                onClick={() => measurePatch(calibrationStatus.currentPatch)}
                disabled={isLoading}
                variant="outline"
                className="flex items-center gap-2"
              >
                {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <CheckCircle className="h-4 w-4" />}
                Measure {patches[calibrationStatus.currentPatch]?.name}
              </Button>
            )}

            {patches.filter(p => p.status === 'complete').length >= 4 && (
              <Button
                onClick={computeMatrix}
                disabled={isLoading}
                variant="outline"
                className="flex items-center gap-2"
              >
                {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <CheckCircle className="h-4 w-4" />}
                Compute Matrix (Tikhonov)
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <XCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Calibration Patches Grid */}
      <Card>
        <CardHeader>
          <CardTitle>ColorChecker Patches</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {patches.map((patch, index) => (
              <div
                key={index}
                className={`p-4 border rounded-lg ${
                  index === calibrationStatus.currentPatch && calibrationStatus.isActive
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200'
                }`}
              >
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-semibold text-sm">{patch.name}</h4>
                  <Badge
                    variant={
                      patch.status === 'complete' ? 'default' :
                      patch.status === 'measuring' ? 'secondary' :
                      patch.status === 'error' ? 'destructive' : 'outline'
                    }
                  >
                    {patch.status}
                  </Badge>
                </div>

                <div className="flex gap-2 mb-2">
                  <div
                    className="w-8 h-8 border rounded"
                    style={{ backgroundColor: `rgb(${patch.ref_r}, ${patch.ref_g}, ${patch.ref_b})` }}
                    title="Reference Color"
                  />
                  {patch.status === 'complete' && (
                    <div
                      className="w-8 h-8 border rounded"
                      style={{ backgroundColor: `rgb(${patch.measured_r}, ${patch.measured_g}, ${patch.measured_b})` }}
                      title="Measured Color"
                    />
                  )}
                </div>

                {patch.status === 'complete' && (
                  <div className="space-y-1 text-xs">
                    <div className="flex justify-between">
                      <span>ΔE 2000:</span>
                      <span className={`font-semibold ${getDeltaEColor(patch.deltaE)}`}>
                        {patch.deltaE.toFixed(2)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Quality:</span>
                      <span className={getDeltaELabel(patch.deltaE)}></span>
                    </div>
                    <div className="flex justify-between">
                      <span>Confidence:</span>
                      <span className="font-semibold">{(patch.confidence * 100).toFixed(1)}%</span>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Calibration Results */}
      {calibrationStatus.matrixValid && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              Calibration Results
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-2">Quality Metrics</h4>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Overall Quality:</span>
                    <span className={`font-semibold ${getQualityColor(calibrationStatus.quality)}`}>
                      {(calibrationStatus.quality * 100).toFixed(1)}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Average ΔE 2000:</span>
                    <span className={`font-semibold ${getDeltaEColor(calibrationStatus.averageDeltaE)}`}>
                      {calibrationStatus.averageDeltaE.toFixed(2)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Patches Measured:</span>
                    <span className="font-semibold">
                      {patches.filter(p => p.status === 'complete').length}/{patches.length}
                    </span>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-semibold mb-2">Recommendations</h4>
                <div className="space-y-1 text-sm">
                  {calibrationStatus.averageDeltaE < 2 && (
                    <div className="text-green-600">✓ Excellent calibration quality</div>
                  )}
                  {calibrationStatus.averageDeltaE >= 2 && calibrationStatus.averageDeltaE < 5 && (
                    <div className="text-blue-600">• Good calibration, suitable for most applications</div>
                  )}
                  {calibrationStatus.averageDeltaE >= 5 && (
                    <div className="text-orange-600">⚠ Consider recalibration for better accuracy</div>
                  )}
                  {sensorMetrics?.irContamination && sensorMetrics.irContamination > 0.3 && (
                    <div className="text-yellow-600">• High IR contamination detected</div>
                  )}
                  {calibrationStatus.quality > 0.8 && (
                    <div className="text-green-600">✓ Matrix computation stable</div>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
