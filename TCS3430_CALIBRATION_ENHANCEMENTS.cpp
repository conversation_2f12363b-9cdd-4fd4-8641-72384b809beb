// TCS3430Calibration Enhanced Methods Implementation
// Add these methods to src/TCS3430Calibration.cpp

// ============================================================================
// LAB COLOR SPACE CONVERSION FOR ACCURATE DELTA E
// ============================================================================

void TCS3430Calibration::srgbToLab(uint8_t r, uint8_t g, uint8_t b, float& L, float& a, float& b_lab) {
    // Convert sRGB to linear RGB
    float linear_r = removeGammaCorrection(r / 255.0f);
    float linear_g = removeGammaCorrection(g / 255.0f);
    float linear_b = removeGammaCorrection(b / 255.0f);

    // Convert linear RGB to XYZ (sRGB matrix)
    float X = 0.4124564f * linear_r + 0.3575761f * linear_g + 0.1804375f * linear_b;
    float Y = 0.2126729f * linear_r + 0.7151522f * linear_g + 0.0721750f * linear_b;
    float Z = 0.0193339f * linear_r + 0.1191920f * linear_g + 0.9503041f * linear_b;

    // Normalize by D65 white point
    X /= 0.95047f;
    Y /= 1.00000f;
    Z /= 1.08883f;

    // Convert XYZ to LAB
    auto f = [](float t) -> float {
        return (t > 0.008856f) ? pow(t, 1.0f/3.0f) : (7.787f * t + 16.0f/116.0f);
    };

    float fx = f(X);
    float fy = f(Y);
    float fz = f(Z);

    L = 116.0f * fy - 16.0f;
    a = 500.0f * (fx - fy);
    b_lab = 200.0f * (fy - fz);
}

float TCS3430Calibration::applyGammaCorrection(float linear) {
    if (linear <= 0.0031308f) {
        return 12.92f * linear;
    } else {
        return 1.055f * pow(linear, 1.0f/2.4f) - 0.055f;
    }
}

float TCS3430Calibration::removeGammaCorrection(float srgb) {
    if (srgb <= 0.04045f) {
        return srgb / 12.92f;
    } else {
        return pow((srgb + 0.055f) / 1.055f, 2.4f);
    }
}

// Enhanced Delta E calculation using LAB color space
float TCS3430Calibration::calculateDeltaE(uint8_t r1, uint8_t g1, uint8_t b1,
                                         uint8_t r2, uint8_t g2, uint8_t b2) {
    // Convert both colors to LAB color space
    float L1, a1, b1_lab, L2, a2, b2_lab;
    srgbToLab(r1, g1, b1, L1, a1, b1_lab);
    srgbToLab(r2, g2, b2, L2, a2, b2_lab);

    // Calculate Delta E (CIE76 - simpler than CIEDE2000 but adequate for calibration)
    float deltaL = L1 - L2;
    float deltaA = a1 - a2;
    float deltaB = b1_lab - b2_lab;

    return sqrt(deltaL * deltaL + deltaA * deltaA + deltaB * deltaB);
}

// ============================================================================
// PRE-DEFINED COLOR REFERENCE SETS
// ============================================================================

uint8_t TCS3430Calibration::loadColorCheckerReferences() {
    clearCalibrationPoints();

    // Standard ColorChecker patches (simplified set for TCS3430 calibration)
    struct { uint8_t r, g, b; const char* name; } colorChecker[] = {
        {115, 82, 68, "Dark Skin"},
        {194, 150, 130, "Light Skin"},
        {98, 122, 157, "Blue Sky"},
        {87, 108, 67, "Foliage"},
        {133, 128, 177, "Blue Flower"},
        {103, 189, 170, "Bluish Green"},
        {214, 126, 44, "Orange"},
        {80, 91, 166, "Purplish Blue"},
        {193, 90, 99, "Moderate Red"},
        {94, 60, 108, "Purple"},
        {157, 188, 64, "Yellow Green"},
        {224, 163, 46, "Orange Yellow"},
        {56, 61, 150, "Blue"},
        {70, 148, 73, "Green"},
        {175, 54, 60, "Red"},
        {231, 199, 31, "Yellow"},
        {187, 86, 149, "Magenta"},
        {8, 133, 161, "Cyan"},
        {243, 243, 242, "White"},
        {200, 200, 200, "Neutral 8"},
        {160, 160, 160, "Neutral 6.5"},
        {122, 122, 121, "Neutral 5"},
        {85, 85, 85, "Neutral 3.5"},
        {52, 52, 52, "Black"}
    };

    uint8_t loaded = 0;
    for (const auto& patch : colorChecker) {
        if (numReferences < MAX_CALIBRATION_POINTS) {
            if (addCalibrationPoint(patch.r, patch.g, patch.b, patch.name)) {
                loaded++;
            }
        }
    }

    logCalibration("INFO", "Loaded %d ColorChecker references", loaded);
    return loaded;
}

uint8_t TCS3430Calibration::loadDuluxColorReferences() {
    clearCalibrationPoints();

    // Standard primary colors for basic calibration
    struct { uint8_t r, g, b; const char* name; } duluxColors[] = {
        {255, 0, 0, "Red"},
        {0, 255, 0, "Green"},
        {0, 0, 255, "Blue"},
        {255, 255, 0, "Yellow"},
        {255, 0, 255, "Magenta"},
        {0, 255, 255, "Cyan"},
        {255, 255, 255, "White"},
        {0, 0, 0, "Black"},
        {128, 128, 128, "Gray"},
        {255, 128, 0, "Orange"},
        {128, 0, 255, "Purple"},
        {0, 128, 255, "Light Blue"}
    };

    uint8_t loaded = 0;
    for (const auto& color : duluxColors) {
        if (numReferences < MAX_CALIBRATION_POINTS) {
            if (addCalibrationPoint(color.r, color.g, color.b, color.name)) {
                loaded++;
            }
        }
    }

    logCalibration("INFO", "Loaded %d Dulux color references", loaded);
    return loaded;
}

// ============================================================================
// LOGGING HELPER
// ============================================================================

void TCS3430Calibration::logCalibration(const char* level, const char* format, ...) {
    char buffer[256];
    va_list args;
    va_start(args, format);
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    
    // Use the existing logging system
    if (strcmp(level, "ERROR") == 0) {
        LOG_CAL_ERROR("%s", buffer);
    } else if (strcmp(level, "WARN") == 0) {
        LOG_CAL_WARN("%s", buffer);
    } else if (strcmp(level, "INFO") == 0) {
        LOG_CAL_INFO("%s", buffer);
    } else {
        LOG_CAL_DEBUG("%s", buffer);
    }
}

// ============================================================================
// API HANDLER IMPLEMENTATIONS FOR main.cpp
// ============================================================================

void handleTCS3430CalibrationStatus() {
    if (tcs3430Calibration && tcs3430Calibration->isInitialized()) {
        JsonDocument doc;
        tcs3430Calibration->getCalibrationStatus(doc);
        String response;
        serializeJson(doc, response);
        server.send(200, "application/json", response);
        LOG_WEB_INFO("TCS3430 calibration status served");
    } else {
        server.send(500, "application/json", "{\"error\":\"TCS3430 calibration system not initialized\"}");
        LOG_WEB_ERROR("TCS3430 calibration status requested but system not initialized");
    }
}

void handleTCS3430CalibrationAutoZero() {
    if (!tcs3430Calibration || !tcs3430Calibration->isInitialized()) {
        server.send(500, "application/json", "{\"error\":\"TCS3430 calibration system not initialized\"}");
        return;
    }

    try {
        bool success = tcs3430Calibration->performAutoZero();
        JsonDocument response;
        response["success"] = success;
        response["message"] = success ? "Auto-zero calibration completed" : "Auto-zero calibration failed";
        
        String responseStr;
        serializeJson(response, responseStr);
        server.send(success ? 200 : 500, "application/json", responseStr);
        
        LOG_WEB_INFO("TCS3430 auto-zero calibration %s", success ? "completed" : "failed");
    } catch (...) {
        server.send(500, "application/json", "{\"error\":\"Auto-zero calibration exception\"}");
        LOG_WEB_ERROR("TCS3430 auto-zero calibration exception");
    }
}

void handleTCS3430CalibrationGetDiagnostics() {
    if (tcs3430Calibration && tcs3430Calibration->isInitialized()) {
        JsonDocument doc;
        tcs3430Calibration->getSensorDiagnostics(doc);
        String response;
        serializeJson(doc, response);
        server.send(200, "application/json", response);
        LOG_WEB_INFO("TCS3430 diagnostics served");
    } else {
        server.send(500, "application/json", "{\"error\":\"TCS3430 calibration system not initialized\"}");
        LOG_WEB_ERROR("TCS3430 diagnostics requested but system not initialized");
    }
}
