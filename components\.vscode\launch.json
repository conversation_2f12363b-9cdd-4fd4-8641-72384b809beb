// AUTOMATICALLY GENERATED FILE. PLEASE DO NOT MODIFY IT MANUALLY
//
// PlatformIO Debugging Solution
//
// Documentation: https://docs.platformio.org/en/latest/plus/debugging.html
// Configuration: https://docs.platformio.org/en/latest/projectconf/sections/env/options/debug/index.html

{
    "version": "0.2.0",
    "configurations": [
        {
            "type": "platformio-debug",
            "request": "launch",
            "name": "PIO Debug",
            "executable": "G:/colormatching_x_version/.pio/build/esp32-s3-devkitc-1/firmware.elf",
            "projectEnvName": "esp32-s3-devkitc-1",
            "toolchainBinDir": "C:/Users/<USER>/.platformio/packages/toolchain-xtensa-esp32s3/bin",
            "internalConsoleOptions": "openOnSessionStart",
            "preLaunchTask": {
                "type": "PlatformIO",
                "task": "Pre-Debug"
            }
        },
        {
            "type": "platformio-debug",
            "request": "launch",
            "name": "PIO Debug (skip Pre-Debug)",
            "executable": "G:/colormatching_x_version/.pio/build/esp32-s3-devkitc-1/firmware.elf",
            "projectEnvName": "esp32-s3-devkitc-1",
            "toolchainBinDir": "C:/Users/<USER>/.platformio/packages/toolchain-xtensa-esp32s3/bin",
            "internalConsoleOptions": "openOnSessionStart"
        },
        {
            "type": "platformio-debug",
            "request": "launch",
            "name": "PIO Debug (without uploading)",
            "executable": "G:/colormatching_x_version/.pio/build/esp32-s3-devkitc-1/firmware.elf",
            "projectEnvName": "esp32-s3-devkitc-1",
            "toolchainBinDir": "C:/Users/<USER>/.platformio/packages/toolchain-xtensa-esp32s3/bin",
            "internalConsoleOptions": "openOnSessionStart",
            "loadMode": "manual"
        }
    ]
}
