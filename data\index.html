<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ESP32 Color Matcher</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    /* Custom scrollbar for better aesthetics in dark theme */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    ::-webkit-scrollbar-track {
      background: #1e293b; /* slate-800 */
      border-radius: 10px;
    }
    ::-webkit-scrollbar-thumb {
      background: #475569; /* slate-600 */
      border-radius: 10px;
    }
    ::-webkit-scrollbar-thumb:hover {
      background: #64748b; /* slate-500 */
    }
    /* Simple spinner animation for dark theme */
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    .spinner {
      border: 4px solid rgba(255, 255, 255, 0.1); /* Lighter border for dark bg */
      border-left-color: #3b82f6; /* blue-500 */
      border-radius: 50%;
      width: 24px;
      height: 24px;
      animation: spin 1s linear infinite;
    }
  </style>

<link rel="stylesheet" href="/index.css">
<script type="importmap">
{
  "imports": {
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/",
    "react": "https://esm.sh/react@^19.1.0"
  }
}
</script>
  <script type="module" crossorigin src="/assets/index-DEii3-WM.js"></script>
</head>
<body class="bg-slate-900 text-slate-200">
  <div id="root"></div>
</body>
</html>