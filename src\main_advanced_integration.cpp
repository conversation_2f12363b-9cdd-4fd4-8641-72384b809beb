// ============================================================================
// MAIN.CPP INTEGRATION FOR ADVANCED TCS3430 CALIBRATION
// Complete replacement for existing calibration methods
// ============================================================================

#include <Arduino.h>
#include <WiFi.h>
#include <WebServer.h>
#include <ArduinoJson.h>
#include "AdvancedTCS3430Calibration.h"
#include "DFRobot_TCS3430.h"
#include "config.h"

// ============================================================================
// GLOBAL OBJECTS - REPLACE EXISTING CALIBRATION OBJECTS
// ============================================================================

DFRobot_TCS3430 TCS3430;
AdvancedTCS3430Calibration* advancedCalibration = nullptr;
WebServer server(80);

// LED control pin
const int LED_PIN = 5;

// ============================================================================
// ADVANCED CALIBRATION API HANDLERS - REPLACE ALL EXISTING HANDLERS
// ============================================================================

void handleAdvancedCalibrationInitialize() {
    LOG_WEB_INFO("Advanced calibration initialization requested");
    
    if (!advancedCalibration) {
        advancedCalibration = new AdvancedTCS3430Calibration(&TCS3430, LED_PIN);
    }
    
    bool success = advancedCalibration->initialize();
    
    JsonDocument response;
    response["success"] = success;
    response["message"] = success ? "Advanced calibration system initialized" : "Initialization failed";
    
    String responseStr;
    serializeJson(response, responseStr);
    server.send(success ? 200 : 500, "application/json", responseStr);
}

void handleAdvancedCalibrationEnablePID() {
    if (!advancedCalibration || !advancedCalibration->isInitialized()) {
        server.send(500, "application/json", "{\"error\":\"Calibration system not initialized\"}");
        return;
    }
    
    JsonDocument doc;
    deserializeJson(doc, server.arg("plain"));
    
    float target = doc["target"] | 30000.0f;
    float kp = doc["kp"] | 2.0f;
    float ki = doc["ki"] | 0.1f;
    float kd = doc["kd"] | 0.05f;
    
    advancedCalibration->initializePIDController(kp, ki, kd);
    
    JsonDocument response;
    response["success"] = true;
    response["message"] = "PID controller enabled";
    response["target"] = target;
    response["kp"] = kp;
    response["ki"] = ki;
    response["kd"] = kd;
    
    String responseStr;
    serializeJson(response, responseStr);
    server.send(200, "application/json", responseStr);
    
    LOG_WEB_INFO("PID controller enabled: target=%.0f, Kp=%.2f, Ki=%.2f, Kd=%.2f", target, kp, ki, kd);
}

void handleAdvancedCalibrationSensorMetrics() {
    if (!advancedCalibration || !advancedCalibration->isInitialized()) {
        server.send(500, "application/json", "{\"error\":\"Calibration system not initialized\"}");
        return;
    }
    
    // Read current sensor data
    auto sensorData = advancedCalibration->readSingleSample();
    
    // Calculate IR contamination
    float irContamination = advancedCalibration->calculateIRContamination(sensorData);
    
    // Check for saturation
    bool saturation = advancedCalibration->isSensorSaturated(sensorData);
    
    JsonDocument response;
    response["r"] = sensorData.r;
    response["g"] = sensorData.g;
    response["b"] = sensorData.b;
    response["c"] = sensorData.c;
    response["ir1"] = sensorData.ir1;
    response["ir2"] = sensorData.ir2;
    response["irContamination"] = irContamination;
    response["saturation"] = saturation;
    response["temperature"] = sensorData.temperature;
    response["timestamp"] = sensorData.timestamp;
    response["ledBrightness"] = advancedCalibration->getCurrentLEDBrightness();
    
    String responseStr;
    serializeJson(response, responseStr);
    server.send(200, "application/json", responseStr);
}

void handleAdvancedCalibrationMeasurePatch() {
    if (!advancedCalibration || !advancedCalibration->isInitialized()) {
        server.send(500, "application/json", "{\"error\":\"Calibration system not initialized\"}");
        return;
    }
    
    JsonDocument doc;
    deserializeJson(doc, server.arg("plain"));
    
    int patchIndex = doc["patchIndex"] | 0;
    int numSamples = doc["numSamples"] | 10;
    int stabilizationDelay = doc["stabilizationDelay"] | 50;
    bool useIRCompensation = doc["useIRCompensation"] | true;
    
    LOG_WEB_INFO("Measuring patch %d with %d samples", patchIndex, numSamples);
    
    try {
        // Update LED brightness using PID control for optimal sensor reading
        uint8_t optimalBrightness = advancedCalibration->updateLEDBrightnessPID();
        
        // Wait for LED to stabilize
        delay(100);
        
        // Take averaged measurement with noise reduction
        auto rawData = advancedCalibration->readAveragedSensorData(numSamples, stabilizationDelay);
        
        // Convert to calibrated XYZ and sRGB
        auto calibratedData = advancedCalibration->rawToXYZ(rawData);
        
        JsonDocument response;
        response["success"] = true;
        response["patchIndex"] = patchIndex;
        response["raw"]["r"] = rawData.r;
        response["raw"]["g"] = rawData.g;
        response["raw"]["b"] = rawData.b;
        response["raw"]["c"] = rawData.c;
        response["raw"]["ir1"] = rawData.ir1;
        response["raw"]["ir2"] = rawData.ir2;
        response["calibrated"]["x"] = calibratedData.x;
        response["calibrated"]["y"] = calibratedData.y;
        response["calibrated"]["z"] = calibratedData.z;
        response["r"] = calibratedData.r_srgb;
        response["g"] = calibratedData.g_srgb;
        response["b"] = calibratedData.b_srgb;
        response["deltaE"] = calibratedData.deltaE;
        response["confidence"] = calibratedData.confidence;
        response["ledBrightness"] = optimalBrightness;
        response["irContamination"] = advancedCalibration->calculateIRContamination(rawData);
        
        String responseStr;
        serializeJson(response, responseStr);
        server.send(200, "application/json", responseStr);
        
        LOG_WEB_INFO("Patch %d measured: RGB(%d,%d,%d), ΔE=%.2f, confidence=%.2f", 
                     patchIndex, calibratedData.r_srgb, calibratedData.g_srgb, calibratedData.b_srgb,
                     calibratedData.deltaE, calibratedData.confidence);
        
    } catch (...) {
        JsonDocument response;
        response["success"] = false;
        response["error"] = "Measurement failed";
        
        String responseStr;
        serializeJson(response, responseStr);
        server.send(500, "application/json", responseStr);
        
        LOG_WEB_ERROR("Failed to measure patch %d", patchIndex);
    }
}

void handleAdvancedCalibrationComputeMatrix() {
    if (!advancedCalibration || !advancedCalibration->isInitialized()) {
        server.send(500, "application/json", "{\"error\":\"Calibration system not initialized\"}");
        return;
    }
    
    JsonDocument doc;
    deserializeJson(doc, server.arg("plain"));
    
    float lambda = doc["lambda"] | 0.01f;
    JsonArray patchesArray = doc["patches"];
    
    if (patchesArray.size() < 4) {
        server.send(400, "application/json", "{\"error\":\"Minimum 4 patches required for matrix computation\"}");
        return;
    }
    
    LOG_WEB_INFO("Computing calibration matrix with %d patches, lambda=%.4f", patchesArray.size(), lambda);
    
    // Clear existing patches and add new ones
    advancedCalibration->clearCalibrationPatches();
    
    for (JsonVariant patchVar : patchesArray) {
        JsonObject patch = patchVar.as<JsonObject>();
        
        // Create raw sensor data from patch
        AdvancedTCS3430Calibration::RawSensorData rawData;
        rawData.r = patch["raw"]["r"];
        rawData.g = patch["raw"]["g"];
        rawData.b = patch["raw"]["b"];
        rawData.c = patch["raw"]["c"];
        rawData.ir1 = patch["raw"]["ir1"];
        rawData.ir2 = patch["raw"]["ir2"];
        rawData.timestamp = millis();
        rawData.temperature = 25.0f;
        
        // Add calibration patch
        advancedCalibration->addCalibrationPatch(
            rawData,
            patch["ref_x"],
            patch["ref_y"], 
            patch["ref_z"],
            patch["name"]
        );
    }
    
    // Compute regularized matrix
    bool success = advancedCalibration->computeRegularizedMatrix(
        nullptr, // Uses internal patches
        advancedCalibration->getNumCalibrationPatches(),
        lambda
    );
    
    JsonDocument response;
    response["success"] = success;
    
    if (success) {
        float quality = advancedCalibration->getCalibrationQuality();
        float avgDeltaE = advancedCalibration->validateCalibrationQuality();
        
        response["quality"] = quality;
        response["averageDeltaE"] = avgDeltaE;
        response["message"] = "Matrix computed successfully with Tikhonov regularization";
        
        // Save calibration data
        advancedCalibration->saveCalibrationData();
        
        LOG_WEB_INFO("Matrix computation successful: quality=%.3f, avgΔE=%.2f", quality, avgDeltaE);
    } else {
        response["error"] = "Matrix computation failed - check patch data quality";
        LOG_WEB_ERROR("Matrix computation failed");
    }
    
    String responseStr;
    serializeJson(response, responseStr);
    server.send(success ? 200 : 500, "application/json", responseStr);
}

void handleAdvancedCalibrationValidate() {
    if (!advancedCalibration || !advancedCalibration->isInitialized()) {
        server.send(500, "application/json", "{\"error\":\"Calibration system not initialized\"}");
        return;
    }
    
    float avgDeltaE = advancedCalibration->validateCalibrationQuality();
    float quality = advancedCalibration->getCalibrationQuality();
    
    JsonDocument response;
    response["averageDeltaE"] = avgDeltaE;
    response["quality"] = quality;
    response["patches"] = JsonArray();
    
    // Add individual patch validation results
    // Note: This would require access to internal patch data
    // Implementation depends on exposing patch data through public methods
    
    String responseStr;
    serializeJson(response, responseStr);
    server.send(200, "application/json", responseStr);
    
    LOG_WEB_INFO("Calibration validation: avgΔE=%.2f, quality=%.3f", avgDeltaE, quality);
}

void handleAdvancedCalibrationStatus() {
    if (!advancedCalibration) {
        server.send(500, "application/json", "{\"error\":\"Advanced calibration system not initialized\"}");
        return;
    }
    
    JsonDocument response;
    advancedCalibration->getCalibrationStatus(response);
    
    String responseStr;
    serializeJson(response, responseStr);
    server.send(200, "application/json", responseStr);
}

void handleAdvancedCalibrationDiagnostics() {
    if (!advancedCalibration || !advancedCalibration->isInitialized()) {
        server.send(500, "application/json", "{\"error\":\"Calibration system not initialized\"}");
        return;
    }
    
    JsonDocument response;
    advancedCalibration->getSensorDiagnostics(response);
    
    String responseStr;
    serializeJson(response, responseStr);
    server.send(200, "application/json", responseStr);
}

// ============================================================================
// SETUP FUNCTION - REPLACE EXISTING CALIBRATION INITIALIZATION
// ============================================================================

void setupAdvancedCalibration() {
    LOG_SETUP_INFO("Initializing Advanced TCS3430 Calibration System");
    
    // Initialize TCS3430 sensor
    if (!TCS3430.begin()) {
        LOG_SETUP_ERROR("Failed to initialize TCS3430 sensor");
        return;
    }
    
    // Configure sensor with optimal settings
    TCS3430.setATIME(150);  // Integration time
    TCS3430.setAGAIN(TCS3430_AGAIN_16X);  // Analog gain
    TCS3430.setWTIME(0);    // Wait time
    
    // Enable sensor
    TCS3430.enableSensor();
    
    // Initialize advanced calibration system
    advancedCalibration = new AdvancedTCS3430Calibration(&TCS3430, LED_PIN);
    
    if (advancedCalibration->initialize()) {
        LOG_SETUP_INFO("Advanced calibration system initialized successfully");
    } else {
        LOG_SETUP_ERROR("Failed to initialize advanced calibration system");
        delete advancedCalibration;
        advancedCalibration = nullptr;
    }
}

// ============================================================================
// WEB SERVER ROUTES - REPLACE EXISTING CALIBRATION ROUTES
// ============================================================================

void setupAdvancedCalibrationRoutes() {
    // Advanced calibration API endpoints
    server.on("/api/advanced-calibration/initialize", HTTP_POST, handleAdvancedCalibrationInitialize);
    server.on("/api/advanced-calibration/enable-pid", HTTP_POST, handleAdvancedCalibrationEnablePID);
    server.on("/api/advanced-calibration/sensor-metrics", HTTP_GET, handleAdvancedCalibrationSensorMetrics);
    server.on("/api/advanced-calibration/measure-patch", HTTP_POST, handleAdvancedCalibrationMeasurePatch);
    server.on("/api/advanced-calibration/compute-matrix", HTTP_POST, handleAdvancedCalibrationComputeMatrix);
    server.on("/api/advanced-calibration/validate", HTTP_GET, handleAdvancedCalibrationValidate);
    server.on("/api/advanced-calibration/status", HTTP_GET, handleAdvancedCalibrationStatus);
    server.on("/api/advanced-calibration/diagnostics", HTTP_GET, handleAdvancedCalibrationDiagnostics);
    
    LOG_SETUP_INFO("Advanced calibration API routes configured");
}

// ============================================================================
// MAIN LOOP INTEGRATION
// ============================================================================

void loopAdvancedCalibration() {
    // Handle any periodic calibration tasks
    if (advancedCalibration && advancedCalibration->isInitialized()) {
        // PID control is handled automatically during measurements
        // No periodic tasks required for advanced calibration
    }
}
