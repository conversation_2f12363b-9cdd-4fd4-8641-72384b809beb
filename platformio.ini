[env:esp32-s3-devkitc-1]
platform = espressif32
board = esp32-s3-devkitc-1
framework = arduino

; Serial Monitor Configuration
monitor_speed = 115200
monitor_filters = esp32_exception_decoder

; Build Configuration
build_flags =
    -DCORE_DEBUG_LEVEL=5
    -DBOARD_HAS_PSRAM
    -mfix-esp32-psram-cache-issue
    -DDEBUG_ESP_PORT=Serial
    -DDEBUG_ESP_CORE
    -DDEBUG_ESP_WIFI
    -DDEBUG_ESP_HTTP_CLIENT
    -g3
    -O0

; Library Dependencies
lib_deps = 
    bblanchon/<PERSON>rduino<PERSON>son@^7.0.0
    adafruit/Adafruit NeoPixel@^1.12.0
    dfrobot/DFRobot_TCS3430@^1.0.0

; Upload Configuration
upload_speed = 115200
upload_resetmethod = hard_reset

; Filesystem Configuration for LittleFS
board_build.filesystem = littlefs
board_build.partitions = huge_app.csv

; Source directories (Note: PlatformIO doesn't support custom src_dir for Arduino framework)
; Files should be in src/ directory, but we'll work around this

; Data directory for web files
data_dir = data
