# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Dependencies
node_modules

# Build outputs
dist
dist-ssr
*.local

# PlatformIO
.pio/
.pioenvs/
.piolibdeps/

# ESP32 firmware files
*.bin
*.elf
*.map
*.tmp
.sconsign*
*.dblite

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/c_cpp_properties.json
!.vscode/launch.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
Thumbs.db
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db

# Temporary files
*.tmp
*~
.#*
\#*#
.#*#

# Python cache (for test scripts)
__pycache__/
*.py[cod]
*$py.class

# ESP32 Color Matcher Specific
# Compiled web assets (built from source)
data/assets/
data/index.html
data/data/

# Archives and temporary files
*.zip
DFRobot_TCS3430-master (1).zip
New Text Document.txt

# Deployment and test files
deploy.log
upload.log
test_*.py
debug_*.py
mock_*.py
