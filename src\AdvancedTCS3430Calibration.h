#ifndef ADVANCED_TCS3430_CALIBRATION_H
#define ADVANCED_TCS3430_CALIBRATION_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <Preferences.h>
#include "DFRobot_TCS3430.h"

// ============================================================================
// ADVANCED TCS3430 CALIBRATION SYSTEM
// Complete replacement for all existing calibration methods
// Features: Tikhonov regularization, PID LED control, noise reduction, IR compensation
// ============================================================================

class AdvancedTCS3430Calibration {
public:
    // ========================================================================
    // CONSTANTS AND STRUCTURES
    // ========================================================================
    
    static const int MAX_CAL_POINTS = 24;
    static const int MATRIX_4X4_SIZE = 16; // 4x4 matrix
    static const int NUM_SAMPLES_DEFAULT = 10;
    static constexpr float REGULARIZATION_LAMBDA = 0.01f;
    static constexpr float TARGET_SENSOR_VALUE = 30000.0f;
    static constexpr float MIN_SENSOR_VALUE = 5000.0f;
    static constexpr float MAX_SENSOR_VALUE = 60000.0f;
    
    struct RawSensorData {
        uint16_t r, g, b, c, ir1, ir2;
        float temperature;
        uint32_t timestamp;
    };
    
    struct CalibratedData {
        float x, y, z;
        uint8_t r_srgb, g_srgb, b_srgb;
        float deltaE;
        float confidence;
    };
    
    struct CalibrationPatch {
        RawSensorData raw;
        float ref_x, ref_y, ref_z;
        uint8_t ref_r, ref_g, ref_b;
        char name[32];
        float deltaE;
        bool valid;
    };
    
    struct PIDController {
        float kp, ki, kd;
        float integral, lastError;
        float outputMin, outputMax;
        uint32_t lastTime;
    };
    
    struct CalibrationMatrix {
        float matrix[MATRIX_4X4_SIZE];
        float scaling[3];
        float irThreshold;
        float quality;
        bool valid;
    };

    // ========================================================================
    // CONSTRUCTOR AND INITIALIZATION
    // ========================================================================
    
    AdvancedTCS3430Calibration(DFRobot_TCS3430* sensor, int ledPin);
    ~AdvancedTCS3430Calibration();
    
    bool initialize();
    bool isInitialized() const { return initialized; }
    void reset();

    // ========================================================================
    // ADVANCED SENSOR READING WITH NOISE REDUCTION
    // ========================================================================
    
    /**
     * @brief Read averaged sensor data with noise reduction
     * @param numSamples Number of samples to average
     * @param stabilizationDelay Delay between samples (ms)
     * @return Averaged and filtered sensor data
     */
    RawSensorData readAveragedSensorData(int numSamples = NUM_SAMPLES_DEFAULT, 
                                        int stabilizationDelay = 50);
    
    /**
     * @brief Read single sensor sample with proper timing
     * @return Raw sensor data
     */
    RawSensorData readSingleSample();
    
    /**
     * @brief Apply statistical filtering to reduce outliers
     * @param samples Array of raw samples
     * @param count Number of samples
     * @return Filtered average
     */
    RawSensorData applyStatisticalFiltering(RawSensorData samples[], int count);

    // ========================================================================
    // PID LED BRIGHTNESS CONTROL
    // ========================================================================
    
    /**
     * @brief Initialize PID controller for LED brightness
     * @param kp Proportional gain
     * @param ki Integral gain  
     * @param kd Derivative gain
     */
    void initializePIDController(float kp = 2.0f, float ki = 0.1f, float kd = 0.05f);
    
    /**
     * @brief Update LED brightness using PID control
     * @param targetValue Target sensor reading (default: 30000)
     * @return New LED brightness (0-255)
     */
    uint8_t updateLEDBrightnessPID(float targetValue = TARGET_SENSOR_VALUE);
    
    /**
     * @brief Set LED brightness directly
     * @param brightness LED brightness (0-255)
     */
    void setLEDBrightness(uint8_t brightness);
    
    /**
     * @brief Get current LED brightness
     * @return Current brightness (0-255)
     */
    uint8_t getCurrentLEDBrightness() const { return currentLEDBrightness; }

    // ========================================================================
    // DYNAMIC IR COMPENSATION
    // ========================================================================
    
    /**
     * @brief Apply dynamic IR compensation based on ambient conditions
     * @param raw Raw sensor data
     * @param x Output X coordinate (modified)
     * @param y Output Y coordinate (modified)
     * @param z Output Z coordinate (modified)
     * @return Compensation factor applied
     */
    float applyDynamicIRCompensation(const RawSensorData& raw, float& x, float& y, float& z);
    
    /**
     * @brief Calculate IR contamination level
     * @param raw Raw sensor data
     * @return IR contamination ratio (0.0 = no contamination, 1.0 = high contamination)
     */
    float calculateIRContamination(const RawSensorData& raw);
    
    /**
     * @brief Set IR compensation parameters
     * @param threshold IR threshold for compensation
     * @param lowWeight Weight for low-IR conditions
     * @param highWeight Weight for high-IR conditions
     */
    void setIRCompensationParameters(float threshold, float lowWeight, float highWeight);

    // ========================================================================
    // TIKHONOV REGULARIZED MATRIX CALIBRATION
    // ========================================================================
    
    /**
     * @brief Compute calibration matrix using Tikhonov regularization
     * @param patches Array of calibration patches
     * @param numPatches Number of patches
     * @param lambda Regularization parameter
     * @return true if computation successful
     */
    bool computeRegularizedMatrix(CalibrationPatch patches[], int numPatches, 
                                 float lambda = REGULARIZATION_LAMBDA);
    
    /**
     * @brief Add calibration patch for matrix computation
     * @param raw Raw sensor reading
     * @param ref_x Reference X coordinate
     * @param ref_y Reference Y coordinate  
     * @param ref_z Reference Z coordinate
     * @param name Patch name
     * @return true if patch added successfully
     */
    bool addCalibrationPatch(const RawSensorData& raw, float ref_x, float ref_y, float ref_z, 
                           const char* name);
    
    /**
     * @brief Clear all calibration patches
     */
    void clearCalibrationPatches();
    
    /**
     * @brief Get number of calibration patches
     * @return Number of patches
     */
    int getNumCalibrationPatches() const { return numPatches; }
    
    /**
     * @brief Validate calibration quality using Delta E
     * @return Average Delta E across all patches
     */
    float validateCalibrationQuality();

    // ========================================================================
    // COLOR SPACE CONVERSIONS
    // ========================================================================
    
    /**
     * @brief Convert raw sensor data to calibrated XYZ
     * @param raw Raw sensor data
     * @return Calibrated XYZ coordinates
     */
    CalibratedData rawToXYZ(const RawSensorData& raw);
    
    /**
     * @brief Convert XYZ to sRGB with proper gamma correction
     * @param x X coordinate
     * @param y Y coordinate
     * @param z Z coordinate
     * @param r Output red (0-255)
     * @param g Output green (0-255)
     * @param b Output blue (0-255)
     */
    void xyzToSRGB(float x, float y, float z, uint8_t& r, uint8_t& g, uint8_t& b);
    
    /**
     * @brief Convert sRGB to LAB color space for Delta E calculation
     * @param r Red component (0-255)
     * @param g Green component (0-255)
     * @param b Blue component (0-255)
     * @param L Output L* component
     * @param a Output a* component
     * @param b_lab Output b* component
     */
    void srgbToLab(uint8_t r, uint8_t g, uint8_t b, float& L, float& a, float& b_lab);
    
    /**
     * @brief Calculate Delta E 2000 color difference
     * @param L1 L* of first color
     * @param a1 a* of first color
     * @param b1 b* of first color
     * @param L2 L* of second color
     * @param a2 a* of second color
     * @param b2 b* of second color
     * @return Delta E 2000 value
     */
    float calculateDeltaE2000(float L1, float a1, float b1, float L2, float a2, float b2);

    // ========================================================================
    // CALIBRATION WORKFLOWS
    // ========================================================================
    
    /**
     * @brief Perform automatic white point calibration
     * @param numSamples Number of samples to average
     * @return true if calibration successful
     */
    bool performWhitePointCalibration(int numSamples = 20);
    
    /**
     * @brief Perform black point calibration (LEDs off)
     * @param numSamples Number of samples to average
     * @return true if calibration successful
     */
    bool performBlackPointCalibration(int numSamples = 20);
    
    /**
     * @brief Load pre-defined ColorChecker patches for calibration
     * @return Number of patches loaded
     */
    int loadColorCheckerPatches();
    
    /**
     * @brief Load Dulux paint color references
     * @return Number of patches loaded
     */
    int loadDuluxColorPatches();
    
    /**
     * @brief Perform complete calibration sequence
     * @param useColorChecker Use ColorChecker patches
     * @param useDulux Use Dulux color patches
     * @return true if calibration successful
     */
    bool performCompleteCalibration(bool useColorChecker = true, bool useDulux = false);

    // ========================================================================
    // DATA PERSISTENCE
    // ========================================================================
    
    /**
     * @brief Save calibration data to NVS
     * @return true if save successful
     */
    bool saveCalibrationData();
    
    /**
     * @brief Load calibration data from NVS
     * @return true if load successful
     */
    bool loadCalibrationData();
    
    /**
     * @brief Export calibration data as JSON
     * @param doc JSON document to populate
     */
    void exportCalibrationData(JsonDocument& doc);
    
    /**
     * @brief Import calibration data from JSON
     * @param doc JSON document containing calibration data
     * @return true if import successful
     */
    bool importCalibrationData(const JsonDocument& doc);

    // ========================================================================
    // DIAGNOSTICS AND STATUS
    // ========================================================================
    
    /**
     * @brief Get comprehensive calibration status
     * @param doc JSON document to populate with status
     */
    void getCalibrationStatus(JsonDocument& doc);
    
    /**
     * @brief Get sensor diagnostics
     * @param doc JSON document to populate with diagnostics
     */
    void getSensorDiagnostics(JsonDocument& doc);
    
    /**
     * @brief Check if sensor is saturated
     * @param raw Raw sensor data
     * @return true if any channel is saturated
     */
    bool isSensorSaturated(const RawSensorData& raw);
    
    /**
     * @brief Get calibration quality metrics
     * @return Quality score (0.0 = poor, 1.0 = excellent)
     */
    float getCalibrationQuality();

private:
    // ========================================================================
    // PRIVATE MEMBERS
    // ========================================================================
    
    DFRobot_TCS3430* tcs3430;
    Preferences preferences;
    
    int ledPin;
    uint8_t currentLEDBrightness;
    bool initialized;
    
    // Calibration data
    CalibrationMatrix calibrationMatrix;
    CalibrationPatch patches[MAX_CAL_POINTS];
    int numPatches;
    
    // PID controller
    PIDController pidController;
    
    // IR compensation
    float irThreshold;
    float irWeightLow;
    float irWeightHigh;
    
    // White and black points
    RawSensorData whitePoint;
    RawSensorData blackPoint;
    bool hasWhitePoint;
    bool hasBlackPoint;
    
    // ========================================================================
    // PRIVATE HELPER METHODS
    // ========================================================================
    
    void initializeDefaults();
    bool solveLinearSystem(float* A, float* b, float* x, int n, int m, float lambda);
    void matrixMultiply(const float* A, const float* B, float* C, int rows_A, int cols_A, int cols_B);
    void matrixTranspose(const float* A, float* AT, int rows, int cols);
    void matrixInverse4x4(const float* A, float* inv);
    float matrixDeterminant4x4(const float* A);
    void logCalibration(const char* level, const char* format, ...);
};

#endif // ADVANCED_TCS3430_CALIBRATION_H
