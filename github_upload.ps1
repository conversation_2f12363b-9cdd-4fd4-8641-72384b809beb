# ESP32 Color Matcher - GitHub Upload Script
# This script safely initializes Git and uploads to GitHub with security checks

param(
    [string]$GitHubUsername = "",
    [string]$RepositoryName = "esp32-color-matching",
    [switch]$SkipSecurityCheck = $false
)

Write-Host "🚀 ESP32 Color Matcher - GitHub Upload Script" -ForegroundColor Cyan
Write-Host "=============================================" -ForegroundColor Cyan

# Function to check for sensitive data
function Test-SensitiveData {
    Write-Host "🔍 Checking for sensitive data..." -ForegroundColor Yellow
    
    $sensitivePatterns = @(
        "Scrofani1985",
        "Wifi 6",
        "192\.168\.0\.152"
    )
    
    $foundSensitive = $false
    
    foreach ($pattern in $sensitivePatterns) {
        $searchResults = Get-ChildItem -Recurse -File | Select-String -Pattern $pattern -SimpleMatch
        if ($searchResults) {
            Write-Host "❌ Found sensitive data: $pattern" -ForegroundColor Red
            foreach ($match in $searchResults) {
                Write-Host "   File: $($match.Filename):$($match.LineNumber)" -ForegroundColor Red
            }
            $foundSensitive = $true
        }
    }
    
    if (-not $foundSensitive) {
        Write-Host "✅ No sensitive data found" -ForegroundColor Green
    }
    
    return $foundSensitive
}

# Function to validate configuration
function Test-Configuration {
    Write-Host "🔧 Validating configuration files..." -ForegroundColor Yellow
    
    $configFiles = @("config.h", "src/config.h")
    $allValid = $true
    
    foreach ($file in $configFiles) {
        if (Test-Path $file) {
            $content = Get-Content $file -Raw
            
            # Check for placeholder values
            if ($content -match "YOUR_WIFI_SSID" -and $content -match "YOUR_WIFI_PASSWORD") {
                Write-Host "✅ $file contains placeholder values" -ForegroundColor Green
            } else {
                Write-Host "❌ $file may contain real credentials" -ForegroundColor Red
                $allValid = $false
            }
        } else {
            Write-Host "⚠️  $file not found" -ForegroundColor Yellow
        }
    }
    
    return $allValid
}

# Security check
if (-not $SkipSecurityCheck) {
    $hasSensitiveData = Test-SensitiveData
    $configValid = Test-Configuration
    
    if ($hasSensitiveData -or -not $configValid) {
        Write-Host ""
        Write-Host "🚨 SECURITY ISSUE DETECTED!" -ForegroundColor Red
        Write-Host "Please fix the issues above before uploading to GitHub." -ForegroundColor Red
        Write-Host ""
        Write-Host "To fix:"
        Write-Host "1. Replace sensitive data with placeholder values"
        Write-Host "2. Review the SECURITY_CLEANUP_GUIDE.md for detailed instructions"
        Write-Host "3. Run this script again or use -SkipSecurityCheck to bypass"
        Write-Host ""
        exit 1
    }
}

# Get GitHub username if not provided
if (-not $GitHubUsername) {
    $GitHubUsername = Read-Host "Enter your GitHub username"
}

Write-Host ""
Write-Host "📋 Configuration:" -ForegroundColor Cyan
Write-Host "   GitHub Username: $GitHubUsername"
Write-Host "   Repository Name: $RepositoryName"
Write-Host "   Repository URL: https://github.com/$GitHubUsername/$RepositoryName"
Write-Host ""

# Confirm before proceeding
$confirm = Read-Host "Continue with GitHub upload? (y/N)"
if ($confirm -ne "y" -and $confirm -ne "Y") {
    Write-Host "Upload cancelled." -ForegroundColor Yellow
    exit 0
}

# Initialize Git repository
Write-Host "📁 Initializing Git repository..." -ForegroundColor Yellow
try {
    if (-not (Test-Path ".git")) {
        git init
        Write-Host "✅ Git repository initialized" -ForegroundColor Green
    } else {
        Write-Host "✅ Git repository already exists" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Failed to initialize Git repository: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Add files to Git
Write-Host "📝 Adding files to Git..." -ForegroundColor Yellow
try {
    # Add .gitignore first
    git add .gitignore
    
    # Add documentation and setup files
    git add README.md SETUP_GUIDE.md SECURITY_CLEANUP_GUIDE.md CALIBRATION_MIGRATION_PLAN.md
    
    # Add source code and configuration
    git add src/ components/ scripts/ docs/
    git add platformio.ini package.json tsconfig.json vite.config.ts
    git add constants.ts types.ts services/
    
    # Add other important files
    git add context.json config.h
    
    Write-Host "✅ Files added to Git staging area" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to add files to Git: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Create initial commit
Write-Host "💾 Creating initial commit..." -ForegroundColor Yellow
try {
    git commit -m "Initial commit: ESP32 Color Matcher with TCS3430 sensor

Features:
- Advanced TCS3430 calibration system with dual-matrix support
- Modern React TypeScript web interface
- Automated deployment scripts
- Comprehensive documentation and setup guides
- Security-cleaned configuration files

Hardware: ESP32-S3 + TCS3430 color sensor
Calibration: Matrix-based colorimetric calibration
Interface: React web app with real-time sensor metrics"

    Write-Host "✅ Initial commit created" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to create commit: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Add remote origin
Write-Host "🔗 Adding GitHub remote..." -ForegroundColor Yellow
try {
    $remoteUrl = "https://github.com/$GitHubUsername/$RepositoryName.git"
    
    # Remove existing origin if it exists
    git remote remove origin 2>$null
    
    git remote add origin $remoteUrl
    Write-Host "✅ GitHub remote added: $remoteUrl" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to add remote: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Set main branch
Write-Host "🌿 Setting main branch..." -ForegroundColor Yellow
try {
    git branch -M main
    Write-Host "✅ Main branch set" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to set main branch: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Push to GitHub
Write-Host "⬆️  Pushing to GitHub..." -ForegroundColor Yellow
Write-Host ""
Write-Host "🔐 You will be prompted for GitHub authentication." -ForegroundColor Cyan
Write-Host "   Use your GitHub username and personal access token (not password)." -ForegroundColor Cyan
Write-Host "   Create a token at: https://github.com/settings/tokens" -ForegroundColor Cyan
Write-Host ""

try {
    git push -u origin main
    Write-Host "✅ Successfully pushed to GitHub!" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to push to GitHub: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Common issues:" -ForegroundColor Yellow
    Write-Host "1. Repository doesn't exist on GitHub - create it first at https://github.com/new"
    Write-Host "2. Authentication failed - use personal access token instead of password"
    Write-Host "3. Network connectivity issues"
    Write-Host ""
    exit 1
}

# Success message
Write-Host ""
Write-Host "🎉 SUCCESS! Your ESP32 Color Matcher project is now on GitHub!" -ForegroundColor Green
Write-Host "============================================================" -ForegroundColor Green
Write-Host ""
Write-Host "📍 Repository URL: https://github.com/$GitHubUsername/$RepositoryName" -ForegroundColor Cyan
Write-Host ""
Write-Host "📋 Next steps for users:" -ForegroundColor Yellow
Write-Host "1. Clone the repository"
Write-Host "2. Follow SETUP_GUIDE.md to configure WiFi credentials"
Write-Host "3. Build and deploy using the provided scripts"
Write-Host ""
Write-Host "📖 Documentation available:" -ForegroundColor Yellow
Write-Host "- README.md - Main project documentation"
Write-Host "- SETUP_GUIDE.md - Configuration and setup instructions"
Write-Host "- CALIBRATION_MIGRATION_PLAN.md - Calibration system details"
Write-Host "- SECURITY_CLEANUP_GUIDE.md - Security best practices"
Write-Host ""
Write-Host "🔧 Development workflow:" -ForegroundColor Yellow
Write-Host "- Use scripts/deploy.ps1 for automated deployment"
Write-Host "- Follow docs/DEVELOPMENT_WORKFLOW.md for best practices"
Write-Host "- Test with provided scripts in the repository"
Write-Host ""
